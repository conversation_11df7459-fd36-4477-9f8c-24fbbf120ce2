<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="assets/img/logo.png" type="image/png">
    <link rel="apple-touch-icon" sizes="180x180" href="https://fxjordan.com/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://fxjordan.com/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://fxjordan.com/favicon-16x16.png">
    <link rel="manifest" href="https://fxjordan.com/site.webmanifest">

    <title>فوركس الأردن | إشارات تداول احترافية</title>

    <!-- Primary Meta Tags -->
    <meta name="title" content="فوركس الأردن | إشارات تداول احترافية">
    <meta name="description"
        content="فوركس الأردن يقدم لك إشارات تداول دقيقة وخطط استثمار ذكية مع أكثر من 15 عاماً من الخبرة في الأسواق المالية. انضم الآن لتحقيق أهدافك المالية!">
    <meta name="keywords"
        content="فوركس الأردن, تداول العملات, إشارات فوركس, تداول العملات الرقمية, إدارة رأس المال, مؤشرات فوركس, توصيات فوركس">
    <meta name="author" content="Forex Jordan">
    <meta name="robots" content="index, follow">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://fxjordan.com/">
    <meta property="og:title" content="فوركس الأردن | إشارات تداول احترافية">
    <meta property="og:description"
        content="خبرة 15 سنة في تقديم إشارات تداول دقيقة وفرص استثمارية متميزة. تعرف على خططنا واشترك الآن!">
    <meta property="og:image" content="https://fxjordan.com/images/forex-banner.jpg">
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://fxjordan.com/">
    <meta property="twitter:title" content="فوركس الأردن | إشارات تداول احترافية">
    <meta property="twitter:description"
        content="انضم إلى فوركس الأردن وتمتع بإشارات تداول دقيقة وخدمات استثمارية احترافية.">
    <meta property="twitter:image" content="https://fxjordan.com/images/forex-banner.jpg">
    <!-- Favicon -->
    <link rel="icon" href="https://fxjordan.com/favicon.ico" type="image/x-icon">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- CSS Custom -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* تحسينات عامة للموقع */
        * {
            box-sizing: border-box;
        }

        body {
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        /* شاشة البداية البسيطة */
        .splash-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 25, 47, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .splash-content {
            background: rgba(16, 36, 70, 0.9);
            border-radius: 15px;
            border: 1px solid rgba(100, 255, 218, 0.3);
            padding: 25px;
            max-width: 500px;
            width: 90%;
            height: auto;
            max-height: 90vh;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* تصميم مضغوط للعناوين */
        .splash-content h2 {
            color: #e6f1ff;
            font-size: 1.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .splash-content p {
            color: #ccd6f6;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .splash-content h3 {
            color: #64ffda;
            font-size: 1rem;
            margin-bottom: 8px;
            font-weight: 600;
        }

        /* تصميم البطاقات المضغوط */
        .feature-card {
            background: rgba(16, 36, 70, 0.7);
            border: 1px solid rgba(100, 255, 218, 0.2);
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .feature-card:hover {
            border-color: rgba(100, 255, 218, 0.4);
            background: rgba(16, 36, 70, 0.9);
        }

        .feature-card h4 {
            color: #64ffda;
            font-size: 1.1rem;
            margin-bottom: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-card h4 i {
            font-size: 1.2rem;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }
        }

        /* تصميم الأزرار المضغوط */
        .splash-btn {
            background: linear-gradient(45deg, #64ffda, #57cbff);
            color: #0a192f;
            border: none;
            padding: 8px 20px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
            margin: 3px;
        }

        .splash-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.4);
        }

        .splash-btn.secondary {
            background: transparent;
            border: 1px solid #64ffda;
            color: #64ffda;
            padding: 7px 19px;
        }

        .splash-btn.secondary:hover {
            background: rgba(100, 255, 218, 0.1);
        }

        /* زر الإغلاق البسيط */
        .close-splash {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #ccd6f6;
            font-size: 1.5rem;
            cursor: pointer;
            transition: color 0.3s ease;
            z-index: 10;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-splash:hover {
            color: #64ffda;
        }

        /* انيميشن بسيط */
        @keyframes logoFloat {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-5px);
            }
        }

        /* تصميم مؤشر الآلة الكاتبة */
        .typing-cursor {
            color: #64ffda;
            animation: blink 1s infinite;
        }

        @keyframes blink {

            0%,
            50% {
                opacity: 1;
            }

            51%,
            100% {
                opacity: 0;
            }
        }

        /* تحسينات شاشة البداية للهواتف */
        @media (max-width: 768px) {
            .splash-content {
                padding: 15px;
                margin: 8px;
                max-width: 96%;
                max-height: 95vh;
                border-radius: 12px;
            }

            .feature-card {
                padding: 8px;
                margin-bottom: 4px;
                min-width: 65px;
                border-radius: 8px;
            }

            .splash-content h2 {
                font-size: 1.3rem;
                margin-bottom: 10px;
            }

            .splash-content h3 {
                font-size: 0.85rem;
                margin-bottom: 6px;
            }

            .splash-content p {
                font-size: 0.75rem;
                margin-bottom: 6px;
                line-height: 1.4;
            }

            .splash-btn {
                padding: 8px 15px;
                font-size: 0.75rem;
                display: block;
                margin: 6px auto;
                max-width: 170px;
                border-radius: 6px;
            }

            .splash-content img {
                height: 45px !important;
                margin-bottom: 10px !important;
            }
        }

        /* تحسينات للهواتف الصغيرة جداً */
        @media (max-width: 480px) {
            .splash-content {
                padding: 12px;
                margin: 5px;
                max-width: 98%;
            }

            .splash-content h2 {
                font-size: 1.1rem;
                margin-bottom: 8px;
            }

            .feature-card {
                padding: 6px;
                margin-bottom: 3px;
                min-width: 60px;
            }

            .splash-btn {
                padding: 6px 12px;
                font-size: 0.7rem;
                max-width: 150px;
            }

            .splash-content img {
                height: 40px !important;
                margin-bottom: 8px !important;
            }
        }

        /* تحسينات قسم الهيرو للهواتف */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem !important;
                margin-bottom: 1.5rem !important;
                line-height: 1.1 !important;
            }

            .hero-subtitle {
                font-size: 0.8em !important;
                display: block !important;
                margin-top: 0.5rem !important;
            }

            .hero-description {
                font-size: 1.1rem !important;
                margin-bottom: 2rem !important;
                max-width: 90% !important;
            }

            .hero-buttons {
                gap: 15px !important;
                margin-top: 2rem !important;
                flex-direction: column !important;
                align-items: center !important;
            }

            .hero-btn-primary,
            .hero-btn-secondary {
                padding: 14px 30px !important;
                font-size: 1rem !important;
                width: 100% !important;
                max-width: 280px !important;
                text-align: center !important;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem !important;
                margin-bottom: 1rem !important;
            }

            .hero-subtitle {
                font-size: 0.75em !important;
            }

            .hero-description {
                font-size: 1rem !important;
                margin-bottom: 1.5rem !important;
            }

            .hero-btn-primary,
            .hero-btn-secondary {
                padding: 12px 25px !important;
                font-size: 0.9rem !important;
                max-width: 250px !important;
            }
        }

        /* Smooth scrolling للصفحة */
        html {
            scroll-behavior: smooth;
        }

        /* تحسين التنقل السلس */
        a[href^="#"] {
            scroll-behavior: smooth;
        }

        /* تعديل المسافة للأقسام بسبب الـ navbar الثابت */
        section {
            scroll-margin-top: 100px;
        }

        /* تحسين تأثيرات شريط التنقل */
        .navbar {
            transition: all 0.5s ease;
            border-bottom: 1px solid rgba(100, 255, 218, 0.15);
        }

        .nav-link {
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 18px !important;
            border-radius: 8px;
            margin: 0 5px;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            transition: all 0.4s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 85%;
        }

        .nav-link:hover {
            color: #64ffda !important;
            transform: translateY(-3px);
            background: rgba(100, 255, 218, 0.1);
            box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
        }

        .navbar-brand {
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(100, 255, 218, 0.5));
        }

        /* تصميم القائمة المنسدلة للأدوات */
        .dropdown-menu {
            border: none !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6) !important;
        }

        .dropdown-item {
            border: none !important;
            background: transparent !important;
        }

        .dropdown-item:hover {
            background: rgba(100, 255, 218, 0.1) !important;
            color: #64ffda !important;
            transform: translateX(5px);
        }

        .dropdown-item:hover i {
            color: #64ffda !important;
            transform: scale(1.1);
        }

        .dropdown-toggle::after {
            border-top: 4px solid;
            border-right: 3px solid transparent;
            border-left: 3px solid transparent;
            margin-right: 3px;
        }

        /* تأثيرات انتقالية للقائمة المنسدلة */
        .dropdown-menu {
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            display: block !important;
            visibility: hidden;
        }

        .dropdown:hover .dropdown-menu,
        .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static !important;
                transform: none !important;
                opacity: 1 !important;
                visibility: visible !important;
                background: rgba(10, 25, 47, 0.98) !important;
                border-radius: 8px !important;
                margin: 5px 0 !important;
            }

            .dropdown-item {
                padding: 12px 20px !important;
                font-size: 0.9rem !important;
            }

            /* تحسينات قسم الشركاء للهواتف */
            .luxury-partners-section {
                padding: 60px 0 !important;
            }

            .luxury-partners-section .section-title {
                font-size: 2rem !important;
                margin-bottom: 1rem !important;
                line-height: 1.2 !important;
            }

            .luxury-partners-section .section-subtitle {
                font-size: 1rem !important;
                margin-bottom: 1.5rem !important;
                padding: 0 10px !important;
            }

            .luxury-partner-card {
                padding: 1.2rem !important;
                margin-bottom: 15px !important;
                border-radius: 12px !important;
            }

            .luxury-partner-img-container {
                width: 120px !important;
                height: 60px !important;
                margin-bottom: 12px !important;
            }

            .luxury-partner-card p {
                font-size: 0.9rem !important;
                margin-top: 12px !important;
            }

            /* تحسين التخطيط للهواتف */
            .luxury-partners-section .row {
                gap: 10px !important;
            }

            .luxury-partners-section .col-6 {
                padding: 5px !important;
            }

            /* إخفاء التأثيرات الخلفية على الهواتف لتحسين الأداء */
            .luxury-bg-effects {
                display: none !important;
            }
        }

        /* تحسينات للهواتف الصغيرة جداً */
        @media (max-width: 480px) {
            .luxury-partners-section {
                padding: 40px 0 !important;
            }

            .luxury-partners-section .section-title {
                font-size: 1.6rem !important;
                padding: 0 15px !important;
            }

            .luxury-partners-section .section-subtitle {
                font-size: 0.9rem !important;
                padding: 0 15px !important;
            }

            .luxury-partner-card {
                padding: 1rem !important;
                margin-bottom: 10px !important;
            }

            .luxury-partner-img-container {
                width: 100px !important;
                height: 50px !important;
                margin-bottom: 10px !important;
            }

            .luxury-partner-card p {
                font-size: 0.8rem !important;
                margin-top: 10px !important;
            }

            /* تحسين المسافات */
            .luxury-partners-section .container {
                padding: 0 10px !important;
            }

            .luxury-partners-section .col-6 {
                padding: 3px !important;
            }
        }

        /* تحسينات للأجهزة اللوحية */
        @media (min-width: 769px) and (max-width: 1024px) {
            .luxury-partners-section {
                padding: 100px 0 !important;
            }

            .luxury-partner-card {
                padding: 1.5rem !important;
            }

            .luxury-partner-img-container {
                width: 150px !important;
                height: 75px !important;
            }
        }

        /* مؤشر تقدم التمرير */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #64ffda, #57cbff);
            z-index: 10000;
            transition: width 0.1s ease;
        }

        /* تأثيرات شريط عداد الأسواق */
        @keyframes slideBackground {
            0% {
                background-position: 0% 50%;
            }

            100% {
                background-position: 200% 50%;
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            33% {
                transform: translateY(-10px) rotate(1deg);
            }

            66% {
                transform: translateY(5px) rotate(-1deg);
            }
        }

        /* تأثيرات hover للبطاقات */
        .market-session-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(100, 255, 218, 0.3);
            border-color: rgba(100, 255, 218, 0.5);
        }

        /* حالات الجلسات */
        .session-open {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .session-closed {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .session-pre-market {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .session-weekend {
            background: rgba(156, 163, 175, 0.2);
            color: #9ca3af;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        /* تصميم السويتش */
        #timezone-switch:checked+span {
            background-color: rgba(100, 255, 218, 0.6);
        }

        #timezone-switch:checked+span span {
            transform: translateX(20px);
            background-color: #64ffda;
        }

        /* تأثيرات العداد التنازلي */
        .countdown-box {
            transition: all 0.3s ease;
        }

        .countdown-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(100, 255, 218, 0.2);
        }

        .countdown-number {
            transition: transform 0.1s ease;
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            .market-session-card {
                margin-bottom: 10px;
                padding: 12px !important;
            }

            #market-sessions h3 {
                font-size: 1.2rem !important;
                flex-direction: column !important;
                gap: 5px !important;
            }

            #market-sessions .row {
                gap: 8px !important;
            }

            .market-session-card h4 {
                font-size: 0.9rem !important;
            }

            .market-session-card div[id$="-time"] {
                font-size: 0.85rem !important;
            }
        }
    </style>
</head>

<body style="font-family: 'Tajawal', sans-serif; background-color: #0a192f; color: #fff; overflow-x: hidden;">
    <!-- مؤشر تقدم التمرير -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <div class="splash-screen" id="splashScreen">
        <div class="splash-content">
            <span class="close-splash" id="closeSplash">&times;</span>

            <!-- شعار الشركة المضغوط -->
            <div style="text-align: center; margin-bottom: 15px;">
                <img src="assets/img/ZFX .png" alt="فوركس الأردن"
                    style="height: 60px; border-radius: 50%; margin-bottom: 10px; animation: logoFloat 3s ease-in-out infinite;">
                <h2>مرحباً بكم في فوركس الأردن</h2>
                <p style="color: #64ffda; font-weight: 600; margin-bottom: 10px; font-size: 0.9rem;">
                    <i class="fas fa-handshake" style="margin-left: 5px;"></i> شريكنا المميز: زيل كابيتال ماركت
                </p>
            </div>

            <!-- معلومات الشركة المضغوطة -->
            <div
                style="background: rgba(16, 36, 70, 0.7); border-radius: 8px; padding: 12px; margin-bottom: 12px; border: 1px solid rgba(100, 255, 218, 0.3);">
                <h3 style="color: #64ffda; font-size: 1rem; margin-bottom: 8px; text-align: center; font-weight: 700;">
                    <i class="fas fa-star" style="margin-left: 5px;"></i> من أفضل 10 شركات فوركس عالمياً
                </h3>
                <p style="text-align: center; margin-bottom: 8px; font-size: 0.85rem;">شركة مرخّصة ومعتمدة ضمن أوائل
                    مزودي السيولة في أسواق الفوركس العالمية</p>

                <!-- المميزات المضغوطة -->
                <div style="display: flex; flex-wrap: wrap; gap: 3px; margin-top: 8px; justify-content: center;">
                    <div class="feature-card" style="flex: 1; min-width: 80px; text-align: center;">
                        <i class="fas fa-mosque"
                            style="color: #64ffda; font-size: 0.8rem; display: block; margin-bottom: 2px;"></i>
                        <p style="font-size: 0.7rem; margin: 0; font-weight: 600;">حسابات إسلامية</p>
                    </div>
                    <div class="feature-card" style="flex: 1; min-width: 80px; text-align: center;">
                        <i class="fas fa-chart-line"
                            style="color: #64ffda; font-size: 0.8rem; display: block; margin-bottom: 2px;"></i>
                        <p style="font-size: 0.7rem; margin: 0; font-weight: 600;">سبريد منخفض</p>
                    </div>
                    <div class="feature-card" style="flex: 1; min-width: 80px; text-align: center;">
                        <i class="fas fa-gift"
                            style="color: #64ffda; font-size: 0.8rem; display: block; margin-bottom: 2px;"></i>
                        <p style="font-size: 0.7rem; margin: 0; font-weight: 600;">بونص 50%</p>
                    </div>
                    <div class="feature-card" style="flex: 1; min-width: 80px; text-align: center;">
                        <i class="fas fa-shield-alt"
                            style="color: #64ffda; font-size: 0.8rem; display: block; margin-bottom: 2px;"></i>
                        <p style="font-size: 0.7rem; margin: 0; font-weight: 600;">رافعة مالية</p>
                    </div>
                </div>
            </div>

            <!-- أزرار العمل المضغوطة -->
            <div style="text-align: center; margin-top: 15px;">
                <a href="https://my.zfx-asia.com/reg/truely?agentnumber=Z2918566C1" class="splash-btn" target="_blank">
                    <i class="fas fa-user-plus"></i> سجّل الآن
                </a>
                <a href="#plans" class="splash-btn secondary"
                    onclick="document.getElementById('splashScreen').style.display='none'">
                    <i class="fas fa-eye"></i> عرض الباقات
                </a>
            </div>
        </div>
    </div>
    <!-- شريط التنقل الزجاجي -->
    <nav class="navbar navbar-expand-lg fixed-top"
        style="background: rgba(10, 25, 47, 0.85); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); padding: 15px 0; transition: all 0.5s ease; border-bottom: 1px solid rgba(100, 255, 218, 0.1);">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#"
                style="color: #64ffda; font-weight: 700; font-size: 1.5rem;">
                <img src="assets/img/logo.png" alt="فوركس الأردن"
                    style="height: 40px; margin-left: 10px; border-radius: 50%;">
                فوركس الأردن
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                style="border: 1px solid #64ffda; color: #64ffda;">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto" style="font-weight: 500;">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home"
                            style="color: #64ffda; margin: 0 10px; position: relative; padding: 8px 15px !important;">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">من
                            نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            خدماتنا</a>
                    </li>

                    <!-- قائمة الأدوات المنسدلة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important; display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-tools" style="font-size: 0.9rem;"></i>
                            الأدوات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="toolsDropdown"
                            style="background: rgba(10, 25, 47, 0.95); backdrop-filter: blur(15px); border: 1px solid rgba(100, 255, 218, 0.3); border-radius: 10px; padding: 8px 0; margin-top: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);">

                            <li>
                                <a class="dropdown-item" href="tools/Risk-management"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-chart-pie"
                                        style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    خطط إدارة رأس المال
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="tools/Lotcalculator"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calculator"
                                        style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    حاسبة اللوت
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="tools/Economic-news"
                                    style="color: #ccd6f6; padding: 10px 20px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; border-radius: 6px; margin: 2px 8px;">
                                    <i class="fas fa-calendar-alt"
                                        style="color: #64ffda; font-size: 1rem; width: 16px;"></i>
                                    التقويم الاقتصادي
                                </a>
                            </li>


                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="#plans"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">باقات
                            الاشتراك</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#partners"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">الشركاء</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact"
                            style="color: #ccd6f6; margin: 0 10px; position: relative; padding: 8px 15px !important;">
                            تواصل معنا
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="#plans" class="btn"
                        style="background: transparent; color: #64ffda; border: 1px solid #64ffda; border-radius: 5px; padding: 10px 20px; font-weight: 500; transition: all 0.3s ease; position: relative; overflow: hidden;">
                        <span style="position: relative; z-index: 1;">اشترك الآن</span>
                        <span
                            style="position: absolute; top: 0; left: 0; width: 0; height: 100%; background: rgba(100, 255, 218, 0.1); transition: all 0.3s ease;"></span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- قسم الهيرو -->
    <section id="home" class="hero-section"
        style="height: 100vh; position: relative; display: flex; align-items: center; justify-content: center; padding-top: 80px; overflow: hidden;">
        <!-- Background Video -->
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -2; overflow: hidden;">
            <video autoplay muted loop playsinline
                style="width: 100%; height: 100%; object-fit: cover; filter: brightness(0.4);">
                <source src="assets/videos/Market Loop Background Video - High Resolution.mp4" type="video/mp4">
            </video>
            <div
                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(45deg, rgba(10, 25, 47, 0.7), rgba(100, 255, 218, 0.1)); z-index: 1;">
            </div>
        </div>

        <!-- تحسين Glass Panel with Waves -->
        <div style="position: relative; z-index: 2; width: 92%; max-width: 950px;">
            <div
                style="position: relative; overflow: hidden; background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(20px); border-radius: 25px; border: 1px solid rgba(100, 255, 218, 0.35); box-shadow: 0 15px 50px rgba(0,0,0,0.6); padding: 4rem; text-align: center; transform-style: preserve-3d;">

                <!-- تحسين Waves inside the panel -->
                <svg viewBox="0 0 500 150" preserveAspectRatio="none"
                    style="position: absolute; top: 0; left: 0; width: 250%; height: 250%; opacity: 0.12; z-index: 0; animation: moveWaves 12s linear infinite;">
                    <path d="M0,50 C150,150 350,-50 500,50 L500,0 L0,0 Z" fill="#64ffda"></path>
                </svg>

                <!-- إضافة موجة ثانية -->
                <svg viewBox="0 0 500 150" preserveAspectRatio="none"
                    style="position: absolute; bottom: 0; right: 0; width: 200%; height: 200%; opacity: 0.08; z-index: 0; animation: moveWaves 15s linear infinite reverse;">
                    <path d="M0,100 C150,50 350,150 500,100 L500,150 L0,150 Z" fill="#57cbff"></path>
                </svg>

                <!-- تحسين Text Content -->
                <div style="position: relative; z-index: 1; transform: translateZ(20px);">
                    <h1 class="hero-title"
                        style="font-size: 3.8rem; color: #e6f1ff; margin-bottom: 2rem; line-height: 1.2;">
                        <span
                            style="background: linear-gradient(90deg, #64ffda, #57cbff, #64ffda); -webkit-background-clip: text; background-clip: text; color: transparent; background-size: 200% 100%; animation: gradientShift 4s ease-in-out infinite;">إشارات
                            تداول احترافية</span><br>
                        <span class="hero-subtitle" style="font-size: 0.7em; color: #ccd6f6; font-weight: 600;">خبرة 15
                            عاماً في الأسواق المالية</span>
                    </h1>
                    <p class="hero-description"
                        style="font-size: 1.4rem; color: #ccd6f6; max-width: 750px; margin: 0 auto 3rem; line-height: 1.6; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                        انضم إلى نخبة المتداولين واحصل على أقوى التوصيات مع إشراف مباشر
                    </p>
                    <div class="hero-buttons"
                        style="display: flex; justify-content: center; gap: 25px; flex-wrap: wrap; margin-top: 3rem;">
                        <a href="#plans" class="hero-btn-primary"
                            style="background: linear-gradient(45deg, #64ffda, #57cbff, #64ffda); background-size: 200% 100%; color: #0a192f; padding: 18px 40px; border-radius: 15px; font-weight: 800; box-shadow: 0 12px 30px rgba(100,255,218,0.6); font-size: 1.25rem; text-decoration: none; transition: all 0.5s ease; letter-spacing: 0.5px; animation: buttonGlow 3s ease-in-out infinite alternate;">
                            <i class="fas fa-gem"></i> عرض الباقات
                        </a>
                        <a href="#contact" class="hero-btn-secondary"
                            style="background: transparent; border: 2px solid #64ffda; color: #64ffda; padding: 16px 38px; border-radius: 15px; font-weight: 800; font-size: 1.25rem; text-decoration: none; transition: all 0.5s ease; letter-spacing: 0.5px; backdrop-filter: blur(10px);">
                            <i class="fas fa-headset"></i> تواصل معنا
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- شريط عداد افتتاح الأسواق -->
    <section id="market-sessions"
        style="padding: 25px 0; background: linear-gradient(135deg, rgba(10, 25, 47, 0.95), rgba(17, 34, 64, 0.95)); position: relative; overflow: hidden; border-top: 1px solid rgba(100, 255, 218, 0.2); border-bottom: 1px solid rgba(100, 255, 218, 0.2);">
        <!-- خلفية متحركة -->
        <div
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, rgba(100, 255, 218, 0.05) 0%, rgba(100, 255, 218, 0.1) 50%, rgba(100, 255, 218, 0.05) 100%); background-size: 200% 100%; animation: slideBackground 15s linear infinite; opacity: 0.3;">
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <!-- العنوان والسويتش -->
            <div class="text-center mb-3">
                <div
                    style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 10px; flex-wrap: wrap;">
                    <h3
                        style="color: #e6f1ff; font-size: 1.4rem; font-weight: 700; margin: 0; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-globe-americas"
                            style="color: #64ffda; font-size: 1.2rem; animation: pulse 2s infinite;"></i>
                        جلسات التداول العالمية
                    </h3>

                    <!-- سويتش التوقيت -->
                    <div
                        style="display: flex; align-items: center; gap: 8px; background: rgba(10, 25, 47, 0.8); padding: 5px; border-radius: 20px; border: 1px solid rgba(100, 255, 218, 0.3);">
                        <span style="color: #ccd6f6; font-size: 0.8rem;">GMT</span>
                        <label style="position: relative; display: inline-block; width: 40px; height: 20px;">
                            <input type="checkbox" id="timezone-switch" style="opacity: 0; width: 0; height: 0;">
                            <span
                                style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(100, 255, 218, 0.3); transition: .4s; border-radius: 20px;">
                                <span
                                    style="position: absolute; content: ''; height: 16px; width: 16px; left: 2px; bottom: 2px; background-color: #64ffda; transition: .4s; border-radius: 50%;"></span>
                            </span>
                        </label>
                        <span style="color: #ccd6f6; font-size: 0.8rem;">الأردن</span>
                    </div>
                </div>

                <!-- مؤشر حالة السوق العامة -->
                <div id="market-general-status"
                    style="margin-top: 10px; padding: 6px 15px; border-radius: 15px; display: inline-block; font-size: 0.8rem; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fas fa-circle" style="margin-left: 6px; font-size: 0.6rem;"></i>
                    <span id="general-status-text">جاري التحميل...</span>
                </div>
            </div>

            <!-- شبكة الجلسات -->
            <div class="row g-2">
                <!-- جلسة نيويورك -->
                <div class="col-lg-3 col-md-6">
                    <div class="market-session-card"
                        style="background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(10px); border-radius: 12px; border: 1px solid rgba(100, 255, 218, 0.3); padding: 15px; text-align: center; transition: all 0.3s ease; position: relative; overflow: hidden; height: 100%;">
                        <!-- تأثير خلفي -->
                        <div
                            style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(100, 255, 218, 0.05) 0%, transparent 70%); animation: float 6s ease-in-out infinite; pointer-events: none;">
                        </div>

                        <div style="position: relative; z-index: 2;">
                            <!-- أيقونة العلم -->
                            <div
                                style="width: 35px; height: 35px; background: rgba(100, 255, 218, 0.15); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; border: 2px solid rgba(100, 255, 218, 0.3);">
                                <i class="fas fa-flag-usa" style="color: #64ffda; font-size: 1rem;"></i>
                            </div>

                            <!-- اسم الجلسة -->
                            <h4 style="color: #e6f1ff; font-size: 1rem; font-weight: 600; margin-bottom: 8px;">نيويورك
                            </h4>

                            <!-- الوقت المحلي -->
                            <div
                                style="background: rgba(100, 255, 218, 0.1); border-radius: 6px; padding: 6px; margin-bottom: 8px;">
                                <div id="ny-time" style="color: #64ffda; font-size: 0.95rem; font-weight: 600;">--:--
                                </div>
                                <div id="ny-time-label" style="color: #ccd6f6; font-size: 0.7rem;">GMT</div>
                            </div>

                            <!-- حالة الجلسة -->
                            <div id="ny-status"
                                style="padding: 6px 10px; border-radius: 15px; font-size: 0.8rem; font-weight: 600; margin-bottom: 6px;">
                                <span class="status-text">جاري التحميل...</span>
                            </div>

                            <!-- عداد تنازلي -->
                            <div id="ny-countdown" style="font-size: 0.75rem; display: none; margin-top: 8px;">
                                <div style="color: #8892b0; font-size: 0.7rem; margin-bottom: 4px;">يفتح خلال:</div>
                                <div style="display: flex; justify-content: center; gap: 4px;">
                                    <div
                                        style="background: linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(100, 255, 218, 0.05)); border: 1px solid rgba(100, 255, 218, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="ny-hours" style="color: #64ffda; font-weight: 600; font-size: 0.8rem;">
                                            00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">س</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(100, 255, 218, 0.05)); border: 1px solid rgba(100, 255, 218, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="ny-minutes"
                                            style="color: #64ffda; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">د</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(100, 255, 218, 0.05)); border: 1px solid rgba(100, 255, 218, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="ny-seconds"
                                            style="color: #64ffda; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">ث</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جلسة لندن -->
                <div class="col-lg-3 col-md-6">
                    <div class="market-session-card"
                        style="background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(10px); border-radius: 12px; border: 1px solid rgba(100, 255, 218, 0.3); padding: 15px; text-align: center; transition: all 0.3s ease; position: relative; overflow: hidden; height: 100%;">
                        <!-- تأثير خلفي -->
                        <div
                            style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(87, 203, 255, 0.05) 0%, transparent 70%); animation: float 8s ease-in-out infinite; pointer-events: none;">
                        </div>

                        <div style="position: relative; z-index: 2;">
                            <!-- أيقونة العلم -->
                            <div
                                style="width: 35px; height: 35px; background: rgba(87, 203, 255, 0.15); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; border: 2px solid rgba(87, 203, 255, 0.3);">
                                <i class="fas fa-crown" style="color: #57cbff; font-size: 1rem;"></i>
                            </div>

                            <!-- اسم الجلسة -->
                            <h4 style="color: #e6f1ff; font-size: 1rem; font-weight: 600; margin-bottom: 8px;">لندن</h4>

                            <!-- الوقت المحلي -->
                            <div
                                style="background: rgba(87, 203, 255, 0.1); border-radius: 6px; padding: 6px; margin-bottom: 8px;">
                                <div id="london-time" style="color: #57cbff; font-size: 0.95rem; font-weight: 600;">
                                    --:--</div>
                                <div id="london-time-label" style="color: #ccd6f6; font-size: 0.7rem;">GMT</div>
                            </div>

                            <!-- حالة الجلسة -->
                            <div id="london-status"
                                style="padding: 6px 10px; border-radius: 15px; font-size: 0.8rem; font-weight: 600; margin-bottom: 6px;">
                                <span class="status-text">جاري التحميل...</span>
                            </div>

                            <!-- عداد تنازلي -->
                            <div id="london-countdown" style="font-size: 0.75rem; display: none; margin-top: 8px;">
                                <div style="color: #8892b0; font-size: 0.7rem; margin-bottom: 4px;">يفتح خلال:</div>
                                <div style="display: flex; justify-content: center; gap: 4px;">
                                    <div
                                        style="background: linear-gradient(135deg, rgba(87, 203, 255, 0.15), rgba(87, 203, 255, 0.05)); border: 1px solid rgba(87, 203, 255, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="london-hours"
                                            style="color: #57cbff; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">س</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(87, 203, 255, 0.15), rgba(87, 203, 255, 0.05)); border: 1px solid rgba(87, 203, 255, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="london-minutes"
                                            style="color: #57cbff; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">د</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(87, 203, 255, 0.15), rgba(87, 203, 255, 0.05)); border: 1px solid rgba(87, 203, 255, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="london-seconds"
                                            style="color: #57cbff; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">ث</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جلسة طوكيو -->
                <div class="col-lg-3 col-md-6">
                    <div class="market-session-card"
                        style="background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(10px); border-radius: 12px; border: 1px solid rgba(100, 255, 218, 0.3); padding: 15px; text-align: center; transition: all 0.3s ease; position: relative; overflow: hidden; height: 100%;">
                        <!-- تأثير خلفي -->
                        <div
                            style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255, 107, 107, 0.05) 0%, transparent 70%); animation: float 10s ease-in-out infinite; pointer-events: none;">
                        </div>

                        <div style="position: relative; z-index: 2;">
                            <!-- أيقونة العلم -->
                            <div
                                style="width: 35px; height: 35px; background: rgba(255, 107, 107, 0.15); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; border: 2px solid rgba(255, 107, 107, 0.3);">
                                <i class="fas fa-torii-gate" style="color: #ff6b6b; font-size: 1rem;"></i>
                            </div>

                            <!-- اسم الجلسة -->
                            <h4 style="color: #e6f1ff; font-size: 1rem; font-weight: 600; margin-bottom: 8px;">طوكيو
                            </h4>

                            <!-- الوقت المحلي -->
                            <div
                                style="background: rgba(255, 107, 107, 0.1); border-radius: 6px; padding: 6px; margin-bottom: 8px;">
                                <div id="tokyo-time" style="color: #ff6b6b; font-size: 0.95rem; font-weight: 600;">--:--
                                </div>
                                <div id="tokyo-time-label" style="color: #ccd6f6; font-size: 0.7rem;">GMT</div>
                            </div>

                            <!-- حالة الجلسة -->
                            <div id="tokyo-status"
                                style="padding: 6px 10px; border-radius: 15px; font-size: 0.8rem; font-weight: 600; margin-bottom: 6px;">
                                <span class="status-text">جاري التحميل...</span>
                            </div>

                            <!-- عداد تنازلي -->
                            <div id="tokyo-countdown" style="font-size: 0.75rem; display: none; margin-top: 8px;">
                                <div style="color: #8892b0; font-size: 0.7rem; margin-bottom: 4px;">يفتح خلال:</div>
                                <div style="display: flex; justify-content: center; gap: 4px;">
                                    <div
                                        style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.05)); border: 1px solid rgba(255, 107, 107, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="tokyo-hours"
                                            style="color: #ff6b6b; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">س</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.05)); border: 1px solid rgba(255, 107, 107, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="tokyo-minutes"
                                            style="color: #ff6b6b; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">د</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.05)); border: 1px solid rgba(255, 107, 107, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="tokyo-seconds"
                                            style="color: #ff6b6b; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">ث</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جلسة سيدني -->
                <div class="col-lg-3 col-md-6">
                    <div class="market-session-card"
                        style="background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(10px); border-radius: 12px; border: 1px solid rgba(100, 255, 218, 0.3); padding: 15px; text-align: center; transition: all 0.3s ease; position: relative; overflow: hidden; height: 100%;">
                        <!-- تأثير خلفي -->
                        <div
                            style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255, 193, 7, 0.05) 0%, transparent 70%); animation: float 12s ease-in-out infinite; pointer-events: none;">
                        </div>

                        <div style="position: relative; z-index: 2;">
                            <!-- أيقونة العلم -->
                            <div
                                style="width: 35px; height: 35px; background: rgba(255, 193, 7, 0.15); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; border: 2px solid rgba(255, 193, 7, 0.3);">
                                <i class="fas fa-sun" style="color: #ffc107; font-size: 1rem;"></i>
                            </div>

                            <!-- اسم الجلسة -->
                            <h4 style="color: #e6f1ff; font-size: 1rem; font-weight: 600; margin-bottom: 8px;">سيدني
                            </h4>

                            <!-- الوقت المحلي -->
                            <div
                                style="background: rgba(255, 193, 7, 0.1); border-radius: 6px; padding: 6px; margin-bottom: 8px;">
                                <div id="sydney-time" style="color: #ffc107; font-size: 0.95rem; font-weight: 600;">
                                    --:--</div>
                                <div id="sydney-time-label" style="color: #ccd6f6; font-size: 0.7rem;">GMT</div>
                            </div>

                            <!-- حالة الجلسة -->
                            <div id="sydney-status"
                                style="padding: 6px 10px; border-radius: 15px; font-size: 0.8rem; font-weight: 600; margin-bottom: 6px;">
                                <span class="status-text">جاري التحميل...</span>
                            </div>

                            <!-- عداد تنازلي -->
                            <div id="sydney-countdown" style="font-size: 0.75rem; display: none; margin-top: 8px;">
                                <div style="color: #8892b0; font-size: 0.7rem; margin-bottom: 4px;">يفتح خلال:</div>
                                <div style="display: flex; justify-content: center; gap: 4px;">
                                    <div
                                        style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="sydney-hours"
                                            style="color: #ffc107; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">س</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="sydney-minutes"
                                            style="color: #ffc107; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">د</div>
                                    </div>
                                    <div
                                        style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); padding: 4px 6px; border-radius: 6px; min-width: 28px; text-align: center;">
                                        <div id="sydney-seconds"
                                            style="color: #ffc107; font-weight: 600; font-size: 0.8rem;">00</div>
                                        <div style="color: #8892b0; font-size: 0.6rem;">ث</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مؤشر الوقت العالمي -->
            <div class="text-center mt-3">
                <div
                    style="background: rgba(10, 25, 47, 0.8); border-radius: 8px; padding: 10px 15px; border: 1px solid rgba(100, 255, 218, 0.2); display: inline-block;">
                    <div id="current-timezone-label" style="color: #ccd6f6; font-size: 0.75rem; margin-bottom: 3px;">
                        التوقيت العالمي (GMT)</div>
                    <div id="current-time" style="color: #64ffda; font-size: 1.1rem; font-weight: 700;">--:--:--</div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم من نحن -->
    <section id="about" class="py-5"
        style="padding: 100px 0; background: linear-gradient(to bottom, #0a192f, #112240); position: relative; overflow: hidden;">
        <div class="container">
            <div class="row align-items-center">
                <!-- تحسين البانل الزجاجي -->
                <div class="col-lg-8 mb-5 mb-lg-0" style="position: relative; z-index: 1;">
                    <div class="glass-card"
                        style="background: rgba(10, 25, 47, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 20px; border: 1px solid rgba(100, 255, 218, 0.3); padding: 3rem; box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4); position: relative; overflow: hidden;">

                        <!-- إضافة تأثير خلفي -->
                        <div
                            style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(100, 255, 218, 0.05) 0%, transparent 70%); animation: float 8s ease-in-out infinite; pointer-events: none;">
                        </div>

                        <h2 class="section-title"
                            style="font-size: 2.8rem; font-weight: 800; margin-bottom: 2rem; color: #e6f1ff; position: relative; display: inline-block; z-index: 2;">
                            <span style="color: #64ffda; text-shadow: 0 0 20px rgba(100, 255, 218, 0.5);">فوركس
                                الأردن</span>
                            <span
                                style="position: absolute; bottom: -15px; left: 0; width: 80px; height: 4px; background: linear-gradient(90deg, #64ffda, #57cbff); border-radius: 3px; box-shadow: 0 0 10px rgba(100, 255, 218, 0.5);"></span>
                        </h2>

                        <!-- تأثير الآلة الكاتبة -->
                        <div
                            style="margin-top: 2rem; color: #ccd6f6; font-size: 1.3rem; line-height: 2.1; position: relative; z-index: 2; text-shadow: 0 1px 3px rgba(0,0,0,0.3); min-height: 200px; padding: 30px; background: rgba(17, 34, 64, 0.2); border-radius: 15px; border: 1px solid rgba(100, 255, 218, 0.1); width: 100%; max-width: 100%; margin-left: -15px; margin-right: -15px;">
                            <span id="typed-about" style="display: block; width: 100%;"></span>
                        </div>

                        <!-- تحسين الإحصائيات -->
                        <div class="stats"
                            style="display: flex; justify-content: space-between; margin-top: 3rem; flex-wrap: wrap; color: #ccd6f6; position: relative; z-index: 2;">
                            <div class="stat-item"
                                style="text-align: center; padding: 20px; flex: 1 1 130px; background: rgba(100, 255, 218, 0.05); border-radius: 15px; margin: 5px; border: 1px solid rgba(100, 255, 218, 0.2); transition: all 0.3s ease;">
                                <h3
                                    style="font-size: 2.8rem; font-weight: 800; color: #64ffda; margin-bottom: 5px; text-shadow: 0 0 15px rgba(100, 255, 218, 0.4);">
                                    15+
                                </h3>
                                <p style="font-size: 1rem; font-weight: 600;">سنوات خبرة</p>
                            </div>
                            <div class="stat-item"
                                style="text-align: center; padding: 20px; flex: 1 1 130px; background: rgba(100, 255, 218, 0.05); border-radius: 15px; margin: 5px; border: 1px solid rgba(100, 255, 218, 0.2); transition: all 0.3s ease;">
                                <h3
                                    style="font-size: 2.8rem; font-weight: 800; color: #64ffda; margin-bottom: 5px; text-shadow: 0 0 15px rgba(100, 255, 218, 0.4);">
                                    98%
                                </h3>
                                <p style="font-size: 1rem; font-weight: 600;">دقة الإشارات</p>
                            </div>
                            <div class="stat-item"
                                style="text-align: center; padding: 20px; flex: 1 1 130px; background: rgba(100, 255, 218, 0.05); border-radius: 15px; margin: 5px; border: 1px solid rgba(100, 255, 218, 0.2); transition: all 0.3s ease;">
                                <h3
                                    style="font-size: 2.8rem; font-weight: 800; color: #64ffda; margin-bottom: 5px; text-shadow: 0 0 15px rgba(100, 255, 218, 0.4);">
                                    24/7
                                </h3>
                                <p style="font-size: 1rem; font-weight: 600;">دعم فني</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صورة الفريق الثابتة -->
                <div class="col-lg-4" style="position: relative; z-index: 1;">
                    <div class="about-image"
                        style="position: relative; border-radius: 10px; overflow: hidden; box-shadow: 0 20px 30px rgba(0, 0, 0, 0.4);">
                        <img src="https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?auto=format&fit=crop&w=1470&q=80"
                            alt="فريق فوركس الأردن" style="width: 100%; height: auto; border-radius: 10px;">
                        <div class="image-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(to bottom, rgba(100, 255, 218, 0.1), rgba(10, 25, 47, 0.7));">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الخدمات -->
    <section id="services" class="py-5" style="padding: 100px 0; background: #0a192f; position: relative;">
        <div class="container">
            <div class="section-header text-center mb-5" style="position: relative; z-index: 1;">
                <h2 class="section-title"
                    style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #e6f1ff; position: relative; display: inline-block;">
                    <span style="color: #64ffda;">إشارات التداول</span> المميزة
                    <span
                        style="position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 70px; height: 3px; background: linear-gradient(90deg, #64ffda, #57cbff); border-radius: 3px;"></span>
                </h2>
                <p class="section-subtitle"
                    style="font-size: 1.1rem; color: #ccd6f6; max-width: 700px; margin: 0 auto;">
                    نقدم لعملائنا الكرام أفضل إشارات التداول اليومية مع تحليلات دقيقة وخطط إدارة رأس مال محكمة
                </p>
            </div>

            <div class="row g-4">
                <div class="col-md-4">
                    <div class="service-card"
                        style="background: rgba(10, 25, 47, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 20px; border: 1px solid rgba(100, 255, 218, 0.3); padding: 2.5rem; height: 100%; transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3); position: relative; overflow: hidden;">

                        <!-- إضافة تأثير خلفي للبطاقة -->
                        <div
                            style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(100, 255, 218, 0.03) 0%, transparent 70%); animation: float 10s ease-in-out infinite; pointer-events: none;">
                        </div>

                        <div class="service-icon"
                            style="width: 80px; height: 80px; background: rgba(100, 255, 218, 0.15); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 2rem; border: 2px solid rgba(100, 255, 218, 0.4); position: relative; z-index: 2; box-shadow: 0 10px 25px rgba(100, 255, 218, 0.2);">
                            <i class="fas fa-chart-line"
                                style="font-size: 2rem; color: #64ffda; text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);"></i>
                        </div>
                        <h3 class="service-title"
                            style="font-size: 1.6rem; font-weight: 700; margin-bottom: 1.2rem; color: #e6f1ff; position: relative; z-index: 2; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                            أزواج
                            العملات</h3>
                        <p class="service-text"
                            style="font-size: 1.05rem; line-height: 1.8; color: #ccd6f6; margin-bottom: 2rem; position: relative; z-index: 2; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">
                            إشارات تداول يومية لأهم أزواج العملات مع تحديد نقاط الدخول والخروج وأهداف الربح
                        </p>
                        <ul class="service-features"
                            style="list-style: none; padding: 0; margin: 0; position: relative; z-index: 2;">
                            <li
                                style="padding: 8px 0; color: #ccd6f6; position: relative; padding-right: 30px; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); margin-bottom: 8px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 10px; text-shadow: 0 0 5px rgba(100, 255, 218, 0.5);"></i>
                                تحليل فني دقيق
                            </li>
                            <li
                                style="padding: 8px 0; color: #ccd6f6; position: relative; padding-right: 30px; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); margin-bottom: 8px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 10px; text-shadow: 0 0 5px rgba(100, 255, 218, 0.5);"></i>
                                تحديد نقاط الدخول
                            </li>
                            <li style="padding: 8px 0; color: #ccd6f6; position: relative; padding-right: 30px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 10px; text-shadow: 0 0 5px rgba(100, 255, 218, 0.5);"></i>
                                إدارة مخاطر محكمة
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="service-card"
                        style="background: rgba(10, 25, 47, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 15px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; transition: all 0.3s ease; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);">
                        <div class="service-icon"
                            style="width: 70px; height: 70px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; border: 1px solid rgba(100, 255, 218, 0.3);">
                            <i class="fab fa-bitcoin" style="font-size: 1.8rem; color: #64ffda;"></i>
                        </div>
                        <h3 class="service-title"
                            style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; color: #e6f1ff;">العملات
                            الرقمية</h3>
                        <p class="service-text"
                            style="font-size: 1rem; line-height: 1.7; color: #ccd6f6; margin-bottom: 1.5rem;">
                            إشارات تداول للعملات الرقمية الرئيسية مع تحليل للسوق وفرص التداول اليومية
                        </p>
                        <ul class="service-features" style="list-style: none; padding: 0; margin: 0;">
                            <li style="padding: 5px 0; color: #ccd6f6; position: relative; padding-right: 25px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 7px;"></i>
                                تغطية لأهم العملات
                            </li>
                            <li style="padding: 5px 0; color: #ccd6f6; position: relative; padding-right: 25px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 7px;"></i>
                                تحليل حركة السوق
                            </li>
                            <li style="padding: 5px 0; color: #ccd6f6; position: relative; padding-right: 25px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 7px;"></i>
                                توصيات بزمن الصلاحية
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="service-card"
                        style="background: rgba(10, 25, 47, 0.5); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 15px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; transition: all 0.3s ease; box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);">
                        <div class="service-icon"
                            style="width: 70px; height: 70px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; border: 1px solid rgba(100, 255, 218, 0.3);">
                            <i class="fas fa-coins" style="font-size: 1.8rem; color: #64ffda;"></i>
                        </div>
                        <h3 class="service-title"
                            style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; color: #e6f1ff;">المعادن
                            والمؤشرات</h3>
                        <p class="service-text"
                            style="font-size: 1rem; line-height: 1.7; color: #ccd6f6; margin-bottom: 1.5rem;">
                            إشارات تداول للذهب، الفضة والنفط بالإضافة إلى المؤشرات العالمية الرئيسية
                        </p>
                        <ul class="service-features" style="list-style: none; padding: 0; margin: 0;">
                            <li style="padding: 5px 0; color: #ccd6f6; position: relative; padding-right: 25px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 7px;"></i>
                                تحليل الذهب والنفط
                            </li>
                            <li style="padding: 5px 0; color: #ccd6f6; position: relative; padding-right: 25px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 7px;"></i>
                                مؤشرات الأسهم العالمية
                            </li>
                            <li style="padding: 5px 0; color: #ccd6f6; position: relative; padding-right: 25px;">
                                <i class="fas fa-check-circle"
                                    style="color: #64ffda; position: absolute; right: 0; top: 7px;"></i>
                                توصيات بعيدة المدى
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم خدماتنا بشبكة بطاقات -->
    <section id="our-services" style="padding: 80px 0; background: #0a192f;">
        <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 15px;">
            <!-- العنوان الرئيسي -->
            <div class="text-center mb-5">
                <h2 style="font-size: 2.3rem; color: #e6f1ff; margin-bottom: 15px;">
                    خدماتنا <span style="color: #64ffda;">المتخصصة</span>
                </h2>
                <div
                    style="width: 70px; height: 3px; background: linear-gradient(to right, #64ffda, #57cbff); margin: 0 auto;">
                </div>
            </div>

            <!-- شبكة الخدمات -->
            <div class="services-grid"
                style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 50px;">
                <!-- الخدمة 1 -->
                <div class="service-card"
                    style="background: rgba(10,25,47,0.7); border-radius: 10px; padding: 30px; border: 1px solid rgba(100,255,218,0.15); transition: all 0.3s ease;">
                    <div
                        style="width: 60px; height: 60px; background: rgba(100,255,218,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 1px solid rgba(100,255,218,0.2);">
                        <i class="fas fa-bolt" style="color: #64ffda; font-size: 1.3rem;"></i>
                    </div>
                    <h3 style="color: #e6f1ff; font-size: 1.3rem; margin-bottom: 15px;">إشارات التداول قصيرة المدى</h3>
                    <p style="color: #8892b0; line-height: 1.6; margin-bottom: 20px;">إشارات دقيقة للصفقات السريعة
                        تستهدف تحقيق أرباح خلال ساعات أو أيام قليلة</p>
                    <a href="#"
                        style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                        المزيد من التفاصيل
                        <i class="fas fa-chevron-left" style="margin-right: 8px; font-size: 0.8rem;"></i>
                    </a>
                </div>

                <!-- الخدمة 2 -->
                <div class="service-card"
                    style="background: rgba(10,25,47,0.7); border-radius: 10px; padding: 30px; border: 1px solid rgba(100,255,218,0.15); transition: all 0.3s ease;">
                    <div
                        style="width: 60px; height: 60px; background: rgba(100,255,218,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 1px solid rgba(100,255,218,0.2);">
                        <i class="fas fa-chart-line" style="color: #64ffda; font-size: 1.3rem;"></i>
                    </div>
                    <h3 style="color: #e6f1ff; font-size: 1.3rem; margin-bottom: 15px;">إشارات التداول متوسطة المدى</h3>
                    <p style="color: #8892b0; line-height: 1.6; margin-bottom: 20px;">صفقات مدروسة تغطي فترة زمنية أطول
                        لتحقيق أرباح متوسطة المدى بأقل مخاطرة</p>
                    <a href="#"
                        style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                        المزيد من التفاصيل
                        <i class="fas fa-chevron-left" style="margin-right: 8px; font-size: 0.8rem;"></i>
                    </a>
                </div>

                <!-- الخدمة 3 -->
                <div class="service-card"
                    style="background: rgba(10,25,47,0.7); border-radius: 10px; padding: 30px; border: 1px solid rgba(100,255,218,0.15); transition: all 0.3s ease;">
                    <div
                        style="width: 60px; height: 60px; background: rgba(100,255,218,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 1px solid rgba(100,255,218,0.2);">
                        <i class="fas fa-chess-knight" style="color: #64ffda; font-size: 1.3rem;"></i>
                    </div>
                    <h3 style="color: #e6f1ff; font-size: 1.3rem; margin-bottom: 15px;">إشارات التداول بعيدة المدى</h3>
                    <p style="color: #8892b0; line-height: 1.6; margin-bottom: 20px;">استراتيجيات طويلة الأجل تعتمد على
                        التحليل الأساسي والفني المتقدم لتحقيق أرباح كبيرة</p>
                    <a href="#"
                        style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                        المزيد من التفاصيل
                        <i class="fas fa-chevron-left" style="margin-right: 8px; font-size: 0.8rem;"></i>
                    </a>
                </div>

                <!-- الخدمة 4 -->
                <div class="service-card"
                    style="background: rgba(10,25,47,0.7); border-radius: 10px; padding: 30px; border: 1px solid rgba(100,255,218,0.15); transition: all 0.3s ease;">
                    <div
                        style="width: 60px; height: 60px; background: rgba(100,255,218,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 1px solid rgba(100,255,218,0.2);">
                        <i class="fas fa-wallet" style="color: #64ffda; font-size: 1.3rem;"></i>
                    </div>
                    <h3 style="color: #e6f1ff; font-size: 1.3rem; margin-bottom: 15px;">اجتياز الحسابات الممولة</h3>
                    <p style="color: #8892b0; line-height: 1.6; margin-bottom: 20px;">مساعدتك في اجتياز اختبارات التحدي
                        للحصول على حساب تداول ممول باحترافية</p>
                    <a href="#"
                        style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                        المزيد من التفاصيل
                        <i class="fas fa-chevron-left" style="margin-right: 8px; font-size: 0.8rem;"></i>
                    </a>
                </div>

                <!-- الخدمة 5 -->
                <div class="service-card"
                    style="background: rgba(10,25,47,0.7); border-radius: 10px; padding: 30px; border: 1px solid rgba(100,255,218,0.15); transition: all 0.3s ease;">

                    <div
                        style="width: 60px; height: 60px; background: rgba(100,255,218,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 1px solid rgba(100,255,218,0.2);">
                        <i class="fas fa-coins" style="color: #64ffda; font-size: 1.3rem;"></i>
                    </div>

                    <h3 style="color: #e6f1ff; font-size: 1.3rem; margin-bottom: 15px;">خطط إدارة رأس المال</h3>

                    <p style="color: #8892b0; line-height: 1.6; margin-bottom: 20px;">
                        استراتيجيات متكاملة لإدارة المخاطر وحماية رأس المال مع تعظيم الأرباح
                    </p>

                    <a href="tools/Risk-management"
                        style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                        المزيد من التفاصيل
                        <i class="fas fa-chevron-left" style="margin-right: 8px; font-size: 0.8rem;"></i>
                    </a>
                </div>


                <!-- الخدمة 6 (اختيارية) -->
                <div class="service-card"
                    style="background: rgba(10,25,47,0.7); border-radius: 10px; padding: 30px; border: 1px solid rgba(100,255,218,0.15); transition: all 0.3s ease;">
                    <div
                        style="width: 60px; height: 60px; background: rgba(100,255,218,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px; border: 1px solid rgba(100,255,218,0.2);">
                        <i class="fas fa-chart-pie" style="color: #64ffda; font-size: 1.3rem;"></i>
                    </div>
                    <h3 style="color: #e6f1ff; font-size: 1.3rem; margin-bottom: 15px;"> تحديثات الأخبار الاقتصادية
                        المباشرة</h3>
                    <p style="color: #8892b0; line-height: 1.6; margin-bottom: 20px;">نوفّر لك خدمة عرض لحظي لأهم
                        الأخبار الاقتصادية المؤثرة على الأسواق العالمية</p>
                    <a href="tools/Economic-news"
                        style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                        المزيد من التفاصيل
                        <i class="fas fa-chevron-left" style="margin-right: 8px; font-size: 0.8rem;"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم باقات الاشتراك -->
    <!-- قسم الباقات ثلاثي الأبعاد -->
    <section id="plans"
        style="padding: 120px 0; background: linear-gradient(to bottom, #112240, #0a192f); position: relative; overflow: hidden; perspective: 1000px;">

        <!-- موجات خلفية ثلاثية الأبعاد -->
        <div class="3d-waves" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 0;">
            <div class="wave"
                style="position: absolute; bottom: 0; left: 0; width: 200%; height: 100%; background: radial-gradient(ellipse at center, rgba(100, 255, 218, 0.05) 0%, rgba(100, 255, 218, 0) 70%); animation: wave-animation 15s linear infinite; transform-origin: 50% 100%;">
            </div>
            <div class="wave"
                style="position: absolute; bottom: 0; left: 0; width: 200%; height: 120%; background: radial-gradient(ellipse at center, rgba(87, 203, 255, 0.03) 0%, rgba(87, 203, 255, 0) 70%); animation: wave-animation 20s linear infinite reverse; transform-origin: 50% 100%;">
            </div>
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <!-- العنوان الرئيسي -->
            <div class="section-header text-center mb-5" style="transform-style: preserve-3d;">
                <h2
                    style="font-size: 2.8rem; font-weight: 700; margin-bottom: 1rem; color: #e6f1ff; position: relative; display: inline-block; transform: translateZ(30px);">
                    باقات <span style="color: #64ffda;">الاشتراك</span>
                    <span
                        style="position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%) translateZ(20px); width: 80px; height: 4px; background: linear-gradient(90deg, #64ffda, #57cbff); border-radius: 3px;"></span>
                </h2>
                <p
                    style="font-size: 1.2rem; color: #ccd6f6; max-width: 700px; margin: 1.5rem auto 0; transform: translateZ(20px);">
                    اختر الباقة التي تناسبك واستفد من إشارات التداول الاحترافية مع دعم فني على مدار الساعة
                </p>
            </div>

            <!-- بطاقات الباقات ثلاثية الأبعاد -->
            <div class="row g-4 justify-content-center" style="transform-style: preserve-3d;">
                <!-- الباقة العادية -->
                <div class="col-lg-4 col-md-6">
                    <div class="3d-plan-card"
                        style="background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(15px); border-radius: 20px; border: 1px solid rgba(100, 255, 218, 0.25); padding: 2.5rem; height: 100%; transition: all 0.5s ease; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3); transform-style: preserve-3d; transform: translateZ(20px); position: relative; overflow: hidden;">
                        <!-- موجة البطاقة -->
                        <div class="card-wave"
                            style="position: absolute; bottom: -50px; left: -50px; width: 200%; height: 200%; background: radial-gradient(circle, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 70%); border-radius: 40%; animation: card-wave 8s linear infinite; opacity: 0.3;">
                        </div>

                        <!-- شارة الباقة -->
                        <div class="plan-badge"
                            style="position: absolute; top: 20px; right: -30px; background: rgba(100, 255, 218, 0.2); color: #64ffda; padding: 5px 40px; transform: rotate(45deg) translateZ(30px); font-size: 0.9rem; font-weight: 600; box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2); z-index: 1;">
                            العادية
                        </div>

                        <!-- محتوى البطاقة -->
                        <div class="plan-content" style="position: relative; z-index: 2; transform-style: preserve-3d;">
                            <div class="plan-header"
                                style="text-align: center; margin-bottom: 2rem; transform: translateZ(30px);">
                                <h3 style="font-size: 1.8rem; font-weight: 700; color: #e6f1ff; margin-bottom: 1rem;">
                                    اشتراك شهري</h3>
                                <div
                                    style="font-size: 2.8rem; font-weight: 700; color: #64ffda; margin-bottom: 0.5rem; transform: translateZ(25px);">
                                    $75
                                    <span style="font-size: 1rem; color: #ccd6f6; font-weight: 400;">/ شهرياً</span>
                                </div>
                                <p style="color: #ccd6f6; font-size: 0.9rem; transform: translateZ(20px);">لمدة شهر واحد
                                </p>
                            </div>

                            <ul style="list-style: none; padding: 0; margin: 0 0 2rem 0; transform: translateZ(20px);">
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    3-7 إشارات تداول يومياً
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    دعم فني على مدار 24 ساعة
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    خطة إدارة رأس مال
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    توصيات لأزواج العملات
                                </li>
                                <li
                                    style="padding: 12px 0; color: #8892b0; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-times"
                                        style="color: #ff5555; position: absolute; right: 0; top: 14px;"></i>
                                    ندوات أسبوعية
                                </li>
                            </ul>

                            <div style="text-align: center; transform: translateZ(25px);">
                                <a href="#contact" class="3d-plan-btn"
                                    style="background: transparent; color: #64ffda; border: 2px solid #64ffda; padding: 14px 30px; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; width: 100%; display: inline-block; position: relative; overflow: hidden;">
                                    <span style="position: relative; z-index: 2;">اشترك الآن</span>
                                    <div class="btn-wave"
                                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent); transform: translateX(-100%);">
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الباقة المميزة -->
                <div class="col-lg-4 col-md-6">
                    <div class="3d-plan-card featured"
                        style="background: rgba(10, 25, 47, 0.8); backdrop-filter: blur(15px); border-radius: 20px; border: 1px solid #64ffda; padding: 2.5rem; height: 100%; transition: all 0.5s ease; box-shadow: 0 25px 50px rgba(100, 255, 218, 0.2); transform-style: preserve-3d; transform: translateY(-20px) translateZ(30px); position: relative; overflow: hidden;">
                        <!-- موجة البطاقة -->
                        <div class="card-wave"
                            style="position: absolute; bottom: -50px; left: -50px; width: 200%; height: 200%; background: radial-gradient(circle, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0) 70%); border-radius: 40%; animation: card-wave 6s linear infinite; opacity: 0.4;">
                        </div>

                        <!-- شارة الباقة -->
                        <div class="plan-badge"
                            style="position: absolute; top: 20px; right: -30px; background: linear-gradient(45deg, #64ffda, #57cbff); color: #0a192f; padding: 5px 40px; transform: rotate(45deg) translateZ(40px); font-size: 0.9rem; font-weight: 600; box-shadow: 0 2px 20px rgba(100, 255, 218, 0.4); z-index: 1;">
                            الأكثر طلباً
                        </div>

                        <!-- محتوى البطاقة -->
                        <div class="plan-content" style="position: relative; z-index: 2; transform-style: preserve-3d;">
                            <div class="plan-header"
                                style="text-align: center; margin-bottom: 2rem; transform: translateZ(40px);">
                                <h3 style="font-size: 2rem; font-weight: 700; color: #e6f1ff; margin-bottom: 1rem;">
                                    بريميوم</h3>
                                <div
                                    style="font-size: 3rem; font-weight: 700; color: #64ffda; margin-bottom: 0.5rem; transform: translateZ(35px);">
                                    $199
                                    <span style="font-size: 1rem; color: #ccd6f6; font-weight: 400;">/ كل 3 أشهر</span>
                                </div>
                                <p style="color: #ccd6f6; font-size: 0.9rem; transform: translateZ(30px);">وفر 11%
                                    شهرياً</p>
                            </div>

                            <ul style="list-style: none; padding: 0; margin: 0 0 2rem 0; transform: translateZ(30px);">
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.3); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(25px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    3-7 إشارات تداول يومياً
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.3); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(25px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    دعم فني على مدار 24 ساعة
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.3); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(25px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    خطة إدارة رأس مال
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.3); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(25px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    توصيات شاملة لجميع الأصول
                                </li>
                                <li
                                    style="padding: 12px 0; color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(25px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    2 ندوات أسبوعية
                                </li>
                            </ul>

                            <div style="text-align: center; transform: translateZ(35px);">
                                <a href="#contact" class="3d-plan-btn"
                                    style="background: linear-gradient(45deg, #64ffda, #57cbff); color: #0a192f; border: none; padding: 14px 30px; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; width: 100%; display: inline-block; position: relative; overflow: hidden;">
                                    <span style="position: relative; z-index: 2;">اشترك الآن</span>
                                    <div class="btn-wave"
                                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent); transform: translateX(-100%);">
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الباقة البلاتينية -->
                <div class="col-lg-4 col-md-6">
                    <div class="3d-plan-card"
                        style="background: rgba(10, 25, 47, 0.7); backdrop-filter: blur(15px); border-radius: 20px; border: 1px solid rgba(100, 255, 218, 0.25); padding: 2.5rem; height: 100%; transition: all 0.5s ease; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3); transform-style: preserve-3d; transform: translateZ(20px); position: relative; overflow: hidden;">
                        <!-- موجة البطاقة -->
                        <div class="card-wave"
                            style="position: absolute; bottom: -50px; left: -50px; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0) 70%); border-radius: 40%; animation: card-wave 10s linear infinite; opacity: 0.3;">
                        </div>

                        <!-- شارة الباقة -->
                        <div class="plan-badge"
                            style="position: absolute; top: 20px; right: -30px; background: rgba(255, 215, 0, 0.2); color: #ffd700; padding: 5px 40px; transform: rotate(45deg) translateZ(30px); font-size: 0.9rem; font-weight: 600; box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2); z-index: 1;">
                            بلاتينيوم
                        </div>

                        <!-- محتوى البطاقة -->
                        <div class="plan-content" style="position: relative; z-index: 2; transform-style: preserve-3d;">
                            <div class="plan-header"
                                style="text-align: center; margin-bottom: 2rem; transform: translateZ(30px);">
                                <h3 style="font-size: 1.8rem; font-weight: 700; color: #e6f1ff; margin-bottom: 1rem;">
                                    سنوي</h3>
                                <div
                                    style="font-size: 2.8rem; font-weight: 700; color: #64ffda; margin-bottom: 0.5rem; transform: translateZ(25px);">
                                    $499
                                    <span style="font-size: 1rem; color: #ccd6f6; font-weight: 400;">/ سنوياً</span>
                                </div>
                                <p style="color: #ccd6f6; font-size: 0.9rem; transform: translateZ(20px);">وفر 30%
                                    شهرياً</p>
                            </div>

                            <ul style="list-style: none; padding: 0; margin: 0 0 2rem 0; transform: translateZ(20px);">
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    3-7 إشارات تداول يومياً
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    دعم فني على مدار 24 ساعة
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    خطة إدارة رأس مال مخصصة
                                </li>
                                <li
                                    style="padding: 12px 0; border-bottom: 1px dashed rgba(100, 255, 218, 0.2); color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    توصيات شاملة لجميع الأصول
                                </li>
                                <li
                                    style="padding: 12px 0; color: #ccd6f6; position: relative; padding-right: 30px; transform: translateZ(15px);">
                                    <i class="fas fa-check"
                                        style="color: #64ffda; position: absolute; right: 0; top: 14px;"></i>
                                    ندوات يومية + تحليلات حصرية
                                </li>
                            </ul>

                            <div style="text-align: center; transform: translateZ(25px);">
                                <a href="#contact" class="3d-plan-btn"
                                    style="background: transparent; color: #64ffda; border: 2px solid #64ffda; padding: 14px 30px; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; width: 100%; display: inline-block; position: relative; overflow: hidden;">
                                    <span style="position: relative; z-index: 2;">اشترك الآن</span>
                                    <div class="btn-wave"
                                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent); transform: translateX(-100%);">
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الشركاء الفاخر -->
    <section id="partners" class="luxury-partners-section"
        style="padding: 150px 0; background: linear-gradient(135deg, #0a192f 0%, #0d2342 100%); position: relative; overflow: hidden;">
        <!-- تأثيرات خلفية فاخرة -->
        <div class="luxury-bg-effects"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 0;">
            <div
                style="position: absolute; top: 15%; left: 5%; width: 400px; height: 400px; border-radius: 50%; background: radial-gradient(circle, rgba(100, 255, 218, 0.12) 0%, rgba(100, 255, 218, 0) 70%); filter: blur(30px);">
            </div>
            <div
                style="position: absolute; bottom: 10%; right: 5%; width: 500px; height: 500px; border-radius: 50%; background: radial-gradient(circle, rgba(87, 203, 255, 0.1) 0%, rgba(87, 203, 255, 0) 70%); filter: blur(40px);">
            </div>
            <div
                style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 800px; height: 800px; border-radius: 50%; background: radial-gradient(circle, rgba(23, 42, 85, 0.3) 0%, rgba(23, 42, 85, 0) 70%); filter: blur(50px);">
            </div>
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <div class="section-header text-center mb-6">
                <h2 class="section-title"
                    style="font-size: 3.2rem; font-weight: 700; margin-bottom: 1.5rem; color: #e6f1ff; position: relative; display: inline-block; letter-spacing: 1px;">
                    <span style="position: relative;">
                        شركاؤنا <span style="color: #64ffda; position: relative;">الاستراتيجيون
                            <svg style="position: absolute; bottom: -15px; left: 0; width: 100%;" width="200"
                                height="20" viewBox="0 0 200 20" preserveAspectRatio="none">
                                <path d="M0,10 Q100,20 200,10" stroke="#64ffda" stroke-width="2" fill="none"
                                    stroke-linecap="round" />
                            </svg>
                        </span>
                    </span>
                </h2>
                <p class="section-subtitle"
                    style="font-size: 1.25rem; color: #ccd6f6; max-width: 800px; margin: 0 auto 2rem auto; line-height: 1.8; letter-spacing: 0.8px;">
                    نختار بعناية شركاءنا لضمان أعلى معايير الجودة والاحترافية. هذه الشراكات الاستراتيجية تعكس التزامنا
                    بتقديم الأفضل لعملائنا الكرام.
                </p>

            </div>

            <div class="row g-3 g-md-4 g-lg-5 justify-content-center">
                <!-- شريك 1 -->
                <div class="col-lg-3 col-md-4 col-6 col-sm-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://sc.myuserhub.com/?pt=27100" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/wb.png" alt="Windsor Brokers"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                Windsor Brokers</p>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-6 col-sm-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://vc.cabinet.oneroyal.com/ar/links/go/7275" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/one-royal.png" alt="OneRoyal"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                OneRoyal</p>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://vc.cabinet.oneroyal.com/ar/links/go/7275" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/justmarkets.png" alt="justmarkets"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                justmarkets</p>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://login-intl.atfx.com/register?redirect_uri=applyLive&invitationCode=WqyD0qDqKoOwnZ%2BZ%2FOUMMw%3D%3D"
                            target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/atfx.png" alt="ATFX"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                ATFX</p>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://my.zfx-asia.com/reg/truely?agentnumber=Z2918566C1" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/ZFX .png" alt="ZFX"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                ZFX</p>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://trade.swissquote.ch/signup/public/form/full/fx/com/individual?lang=ar&partnerid=1bf9a00b-c55f-4aaf-af5a-ad60ad481b39#full/fx/com/individual/step2"
                            target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/bank swissquote.png" alt="Swissquote"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                Swissquote</p>
                        </a>
                    </div>
                </div>

                <!-- شريك 6 -->
                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://www.axi.com/int/live-account?promocode=4687620" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/axi.png" alt="Axi"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                Axi</p>
                        </a>
                    </div>
                </div>

                <!-- شريك 7 -->
                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://www.acy.com/en?affiliate=LTD126233" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/acy.png" alt="ACY"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                ACY</p>
                        </a>
                    </div>
                </div>

                <!-- شريك 8 -->
                <div class="col-lg-3 col-md-4 col-6">
                    <div class="luxury-partner-card"
                        style="background: rgba(16, 36, 70, 0.6); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-radius: 16px; border: 1px solid rgba(100, 255, 218, 0.2); padding: 2rem; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1); box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25); position: relative; overflow: hidden;">
                        <div class="luxury-card-overlay"
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 100%); opacity: 0; transition: all 0.5s ease;">
                        </div>
                        <a href="https://one.exnesstrack.net/a/ufum3vsd" target="_blank"
                            style="display: block; text-align: center; text-decoration: none; position: relative; z-index: 2;">
                            <div class="luxury-partner-img-container"
                                style="width: 180px; height: 90px; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; transition: all 0.4s ease; position: relative;">
                                <div class="luxury-img-frame"
                                    style="position: absolute; width: calc(100% + 20px); height: calc(100% + 20px); border: 2px solid rgba(100, 255, 218, 0.3); border-radius: 8px; opacity: 0; transition: all 0.4s ease;">
                                </div>
                                <img src="assets/img/ex.png" alt="Exness"
                                    style="max-width: 100%; max-height: 100%; object-fit: contain; transition: all 0.4s ease; filter: brightness(1.1);">
                            </div>
                            <p
                                style="color: #e6f1ff; margin-top: 20px; font-size: 1.1rem; font-weight: 600; transition: all 0.4s ease; letter-spacing: 0.5px;">
                                Exness</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم تواصل معنا -->
    <!-- قسم تواصل معنا بتصميم ثلاثي الأبعاد -->
    <section id="contact"
        style="padding: 120px 0; background: linear-gradient(135deg, #112240 0%, #0a192f 100%); position: relative; overflow: hidden;">

        <!-- تأثيرات ثلاثية الأبعاد في الخلفية -->
        <div class="3d-background" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 0;">
            <div
                style="position: absolute; top: 20%; left: 10%; width: 400px; height: 400px; background: radial-gradient(circle, rgba(100, 255, 218, 0.08) 0%, rgba(100, 255, 218, 0) 70%); border-radius: 50%; filter: blur(40px); transform: rotateZ(45deg);">
            </div>
            <div
                style="position: absolute; bottom: 15%; right: 10%; width: 500px; height: 500px; background: radial-gradient(circle, rgba(87, 203, 255, 0.06) 0%, rgba(87, 203, 255, 0) 70%); border-radius: 50%; filter: blur(50px); transform: rotateX(60deg);">
            </div>
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <div class="contact-card-3d"
                        style="background: rgba(10, 25, 47, 0.6); backdrop-filter: blur(12px); border-radius: 20px; border: 1px solid rgba(100, 255, 218, 0.25); padding: 40px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3); transform-style: preserve-3d; transform: perspective(1000px);">

                        <!-- العنوان مع تأثير ثلاثي الأبعاد -->
                        <div style="position: relative; margin-bottom: 30px; transform: translateZ(30px);">
                            <h2
                                style="font-size: 2.5rem; font-weight: 700; color: #e6f1ff; margin-bottom: 15px; position: relative; display: inline-block;">
                                تواصل <span style="color: #64ffda;">معنا</span>
                            </h2>
                            <div
                                style="width: 80px; height: 4px; background: linear-gradient(90deg, #64ffda, #57cbff); border-radius: 2px; transform: translateZ(20px);">
                            </div>
                        </div>

                        <p
                            style="font-size: 1.1rem; color: #ccd6f6; line-height: 1.8; margin-bottom: 30px; transform: translateZ(20px);">
                            لديك استفسار أو ترغب في الاشتراك بخدماتنا؟ نحن هنا لمساعدتك عبر قنوات التواصل التالية:
                        </p>

                        <!-- قنوات التواصل بتأثيرات ثلاثية الأبعاد -->
                        <div class="contact-methods-3d">

                            <!-- بطاقة تيليجرام -->
                            <div class="contact-method-card"
                                style="background: rgba(17, 34, 64, 0.5); border-radius: 12px; padding: 20px; margin-bottom: 20px; border: 1px solid rgba(100, 255, 218, 0.2); transition: all 0.4s ease; transform-style: preserve-3d; transform: translateZ(10px);">
                                <div style="display: flex; align-items: center;">
                                    <div
                                        style="width: 60px; height: 60px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; flex-shrink: 0; border: 1px solid rgba(100, 255, 218, 0.3); transform: translateZ(20px);">
                                        <i class="fab fa-telegram" style="font-size: 1.8rem; color: #64ffda;"></i>
                                    </div>
                                    <div>
                                        <h3 style="font-size: 1.25rem; color: #e6f1ff; margin-bottom: 5px;">تيليجرام
                                        </h3>
                                        <p style="color: #8892b0; margin-bottom: 5px;">@fx_jor</p>
                                        <a href="https://t.me/fx_jor" target="_blank"
                                            style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                                            انضم إلينا <i class="fas fa-external-link-alt"
                                                style="margin-right: 8px; font-size: 0.8rem;"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>


                            <!-- بطاقة واتساب -->
                            <div class="contact-method-card"
                                style="background: rgba(17, 34, 64, 0.5); border-radius: 12px; padding: 20px; margin-bottom: 20px; border: 1px solid rgba(100, 255, 218, 0.2); transition: all 0.4s ease; transform-style: preserve-3d; transform: translateZ(15px);">
                                <div style="display: flex; align-items: center;">
                                    <div
                                        style="width: 60px; height: 60px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; flex-shrink: 0; border: 1px solid rgba(100, 255, 218, 0.3); transform: translateZ(25px);">
                                        <i class="fab fa-telegram" style="font-size: 1.8rem; color: #64ffda;"></i>
                                    </div>
                                    <div>
                                        <h3 style="font-size: 1.25rem; color: #e6f1ff; margin-bottom: 5px;">محمد الأغــا</h3>
                                        <p style="color: #8892b0; margin-bottom: 5px;">+962 79 000 0000</p>
                                        <a href="https://t.me/forexjo" target="_blank"
                                            style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                                            تواصل الآن <i class="fas fa-external-link-alt"
                                                style="margin-right: 8px; font-size: 0.8rem;"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- بطاقة إيميل -->
                            <div class="contact-method-card"
                                style="background: rgba(17, 34, 64, 0.5); border-radius: 12px; padding: 20px; border: 1px solid rgba(100, 255, 218, 0.2); transition: all 0.4s ease; transform-style: preserve-3d; transform: translateZ(20px);">
                                <div style="display: flex; align-items: center;">
                                    <div
                                        style="width: 60px; height: 60px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; flex-shrink: 0; border: 1px solid rgba(100, 255, 218, 0.3); transform: translateZ(30px);">
                                        <i class="fas fa-telegram" style="font-size: 1.8rem; color: #64ffda;"></i>
                                    </div>
                                    <div>
                                        <h3 style="font-size: 1.25rem; color: #e6f1ff; margin-bottom: 5px;">الدعم الفني</h3>
                                        <p style="color: #8892b0; margin-bottom: 5px;">الدعم الفني</p>
                                        <a href="https://t.me/jorSupport"
                                            style="color: #64ffda; text-decoration: none; font-weight: 500; display: inline-flex; align-items: center;">
                                            أرسل رسالة <i class="fas fa-external-link-alt"
                                                style="margin-right: 8px; font-size: 0.8rem;"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العمود الثاني للعناصر ثلاثية الأبعاد -->
                <div class="col-lg-6">
                    <div class="3d-elements"
                        style="position: relative; height: 100%; min-height: 400px; perspective: 1000px;">

                        <!-- عنصر دائري ثلاثي الأبعاد -->
                        <div class="3d-circle"
                            style="position: absolute; top: 50%; left: 50%; width: 300px; height: 300px; background: radial-gradient(circle, rgba(100, 255, 218, 0.15) 0%, rgba(100, 255, 218, 0) 70%); border-radius: 50%; filter: blur(20px); transform: translate(-50%, -50%) rotateX(60deg) rotateZ(45deg); animation: float 8s ease-in-out infinite;">
                        </div>

                        <!-- أيقونات ثلاثية الأبعاد -->
                        <div class="3d-icon"
                            style="position: absolute; top: 30%; right: 10%; transform: translateZ(50px);">
                            <i class="fas fa-headset" style="font-size: 5rem; color: rgba(100, 255, 218, 0.3);"></i>
                        </div>

                        <div class="3d-icon"
                            style="position: absolute; bottom: 20%; left: 15%; transform: translateZ(30px) rotateZ(-15deg);">
                            <i class="fas fa-comments" style="font-size: 4rem; color: rgba(87, 203, 255, 0.3);"></i>
                        </div>

                        <!-- نص ثلاثي الأبعاد -->
                        <div class="3d-text"
                            style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) translateZ(40px); text-align: center;">
                            <h3
                                style="font-size: 1.8rem; color: rgba(230, 241, 255, 0.7); font-weight: 600; text-shadow: 0 0 10px rgba(100, 255, 218, 0.3);">
                                نحن هنا لمساعدتك</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- الفوتر -->
    <footer class="py-5" style="background: #020c1b; padding: 60px 0 30px; position: relative;">
        <div class="container">
            <div class="row">
                <!-- Column 1: About -->
                <div class="col-lg-4 mb-5 mb-lg-0">
                    <div class="footer-about">
                        <a class="d-flex align-items-center mb-3" href="#"
                            style="color: #64ffda; font-weight: 700; font-size: 1.5rem; text-decoration: none;">
                            <img src="https://via.placeholder.com/50x50" alt="فوركس الأردن"
                                style="height: 40px; margin-left: 10px; border-radius: 50%;">
                            فوركس الأردن
                        </a>
                        <p style="color: #8892b0; line-height: 1.7; margin-bottom: 1.5rem;">
                            منصة متخصصة في تقديم إشارات التداول لأزواج العملات، العملات الرقمية، المعادن والمؤشرات
                            العالمية بدقة واحترافية.
                        </p>
                        <div class="social-links" style="display: flex; gap: 15px;">
                            <a href="#"
                                style="width: 40px; height: 40px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #64ffda; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(100, 255, 218, 0.2);">
                                <i class="fab fa-telegram"></i>
                            </a>
                            <a href="#"
                                style="width: 40px; height: 40px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #64ffda; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(100, 255, 218, 0.2);">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#"
                                style="width: 40px; height: 40px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #64ffda; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(100, 255, 218, 0.2);">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#"
                                style="width: 40px; height: 40px; background: rgba(100, 255, 218, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #64ffda; text-decoration: none; transition: all 0.3s ease; border: 1px solid rgba(100, 255, 218, 0.2);">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Column 2: Quick Links -->
                <div class="col-lg-2 col-md-4 mb-4 mb-md-0">
                    <h3
                        style="font-size: 1.3rem; font-weight: 600; margin-bottom: 1.5rem; color: #ccd6f6; position: relative; padding-bottom: 10px;">
                        روابط سريعة
                        <span
                            style="position: absolute; bottom: 0; left: 0; width: 40px; height: 2px; background: #64ffda;"></span>
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#home"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#about"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                من نحن
                            </a>
                        </li>
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#services"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                إشارات التداول
                            </a>
                        </li>
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#plans"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                باقات الاشتراك
                            </a>
                        </li>
                        <li>
                            <a href="#contact"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                تواصل معنا
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: Services -->
                <div class="col-lg-3 col-md-4 mb-4 mb-md-0">
                    <h3
                        style="font-size: 1.3rem; font-weight: 600; margin-bottom: 1.5rem; color: #ccd6f6; position: relative; padding-bottom: 10px;">
                        خدماتنا
                        <span
                            style="position: absolute; bottom: 0; left: 0; width: 40px; height: 2px; background: #64ffda;"></span>
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                إشارات الفوركس
                            </a>
                        </li>
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                إشارات العملات الرقمية
                            </a>
                        </li>
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                إشارات المعادن
                            </a>
                        </li>
                        <li style="margin-bottom: 0.7rem;">
                            <a href="#"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                تحليل فني يومي
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                style="color: #8892b0; text-decoration: none; transition: all 0.3s ease; display: inline-block; position: relative;">
                                <i class="fas fa-chevron-left"
                                    style="margin-left: 5px; font-size: 0.7rem; color: #64ffda;"></i>
                                دورات تعليمية
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div class="col-lg-3 col-md-4">
                    <h3
                        style="font-size: 1.3rem; font-weight: 600; margin-bottom: 1.5rem; color: #ccd6f6; position: relative; padding-bottom: 10px;">
                        تواصل معنا
                        <span
                            style="position: absolute; bottom: 0; left: 0; width: 40px; height: 2px; background: #64ffda;"></span>
                    </h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 1rem; display: flex; align-items: flex-start;">
                            <i class="fas fa-map-marker-alt"
                                style="color: #64ffda; margin-left: 10px; margin-top: 3px;"></i>
                            <span style="color: #8892b0;">عمان، الأردن</span>
                        </li>
                        <li style="margin-bottom: 1rem; display: flex; align-items: center;">
                            <i class="fas fa-phone-alt" style="color: #64ffda; margin-left: 10px;"></i>
                            <a href="tel:+962790000000" style="color: #8892b0; text-decoration: none;">+962 79 000
                                0000</a>
                        </li>
                        <li style="margin-bottom: 1rem; display: flex; align-items: center;">
                            <i class="fas fa-envelope" style="color: #64ffda; margin-left: 10px;"></i>
                            <a href="mailto:<EMAIL>"
                                style="color: #8892b0; text-decoration: none;"><EMAIL></a>
                        </li>
                        <li style="display: flex; align-items: center;">
                            <i class="far fa-clock" style="color: #64ffda; margin-left: 10px;"></i>
                            <span style="color: #8892b0;">الأحد - الخميس: 9 ص - 5 م</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Copyright -->
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <p style="color: #8892b0; margin-bottom: 0; font-size: 0.9rem;">
                        &copy; 2023 فوركس الأردن. جميع الحقوق محفوظة.
                    </p>
                    <p style="color: #8892b0; margin-bottom: 0; font-size: 0.9rem;">
                        &copy; تم التصميم بواسطة 3mrosarayreh
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/typed.js@2.0.16/dist/typed.umd.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/main.js"></script>

    <!-- Market Sessions Clock JavaScript -->
    <script>
        let useJordanTime = false; // متغير لتتبع التوقيت المختار

        // تحديث عداد الأسواق
        function updateMarketSessions() {
            const now = new Date();
            const utcTime = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));

            // تحديث التوقيت الحالي
            updateCurrentTime();

            // تحديث أوقات الجلسات
            updateSessionTime('ny-time', 'ny-time-label', 'America/New_York');
            updateSessionTime('london-time', 'london-time-label', 'Europe/London');
            updateSessionTime('tokyo-time', 'tokyo-time-label', 'Asia/Tokyo');
            updateSessionTime('sydney-time', 'sydney-time-label', 'Australia/Sydney');

            // تحديث حالات الجلسات مع العداد التنازلي
            updateSessionStatus('ny', 13, 22); // نيويورك: 13:00 - 22:00 GMT (1:00 مساءً - 10:00 مساءً)
            updateSessionStatus('london', 8, 17); // لندن: 08:00 - 17:00 GMT (8:00 صباحاً - 5:00 مساءً)
            updateSessionStatus('tokyo', 0, 9); // طوكيو: 00:00 - 09:00 GMT (12:00 صباحاً - 9:00 صباحاً)
            updateSessionStatus('sydney', 22, 7); // سيدني: 22:00 - 07:00 GMT (10:00 مساءً - 7:00 صباحاً)

            // تحديث الحالة العامة للسوق
            updateGeneralMarketStatus();
        }

        // تحديث التوقيت الحالي
        function updateCurrentTime() {
            const currentTimeElement = document.getElementById('current-time');
            const labelElement = document.getElementById('current-timezone-label');

            if (!currentTimeElement || !labelElement) return;

            if (useJordanTime) {
                const jordanTime = new Date().toLocaleTimeString('en-US', {
                    timeZone: 'Asia/Amman',
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                currentTimeElement.textContent = jordanTime;
                labelElement.textContent = 'التوقيت المحلي (الأردن)';
            } else {
                const now = new Date();
                const utcTime = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
                currentTimeElement.textContent = utcTime.toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                labelElement.textContent = 'التوقيت العالمي (GMT)';
            }
        }

        function updateSessionTime(elementId, labelId, timezone) {
            const element = document.getElementById(elementId);
            const labelElement = document.getElementById(labelId);

            if (element && labelElement) {
                if (useJordanTime) {
                    const jordanTime = new Date().toLocaleTimeString('en-US', {
                        timeZone: 'Asia/Amman',
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    element.textContent = jordanTime;
                    labelElement.textContent = 'الأردن';
                } else {
                    const localTime = new Date().toLocaleTimeString('en-US', {
                        timeZone: timezone,
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    element.textContent = localTime;
                    labelElement.textContent = 'محلي';
                }
            }
        }

        function updateSessionStatus(sessionPrefix, gmtOpenHour, gmtCloseHour) {
            const statusElement = document.getElementById(sessionPrefix + '-status');
            const countdownElement = document.getElementById(sessionPrefix + '-countdown');

            if (!statusElement) return;

            // تحديد أوقات الجلسة حسب التوقيت المختار
            let currentTime, currentHour, openHour, closeHour;

            if (useJordanTime) {
                // توقيت الأردن (UTC+3)
                currentTime = new Date();
                const jordanTime = new Date(currentTime.toLocaleString("en-US", { timeZone: "Asia/Amman" }));
                currentHour = jordanTime.getHours() + (jordanTime.getMinutes() / 60) + (jordanTime.getSeconds() / 3600);

                // تحويل أوقات الجلسات إلى توقيت الأردن
                if (sessionPrefix === 'sydney') {
                    openHour = 1; closeHour = 10; // 1:00 صباحاً - 10:00 صباحاً
                } else if (sessionPrefix === 'tokyo') {
                    openHour = 3; closeHour = 12; // 3:00 صباحاً - 12:00 ظهراً
                } else if (sessionPrefix === 'london') {
                    openHour = 11; closeHour = 20; // 11:00 صباحاً - 8:00 مساءً
                } else if (sessionPrefix === 'ny') {
                    openHour = 16; closeHour = 1; // 4:00 مساءً - 1:00 صباحاً (عبر منتصف الليل)
                }
            } else {
                // التوقيت العالمي GMT
                const now = new Date();
                currentTime = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
                currentHour = currentTime.getHours() + (currentTime.getMinutes() / 60) + (currentTime.getSeconds() / 3600);

                // استخدام الأوقات العالمية مباشرة
                openHour = gmtOpenHour;
                closeHour = gmtCloseHour;
            }

            let isOpen = false;
            let statusText = '';
            let statusClass = '';
            let showCountdown = true;

            // التحقق من أوقات العطلة الأسبوعية
            const isWeekendClosed = checkWeekendStatus(currentTime, useJordanTime);

            if (isWeekendClosed.isClosed) {
                statusText = 'عطلة أسبوعية';
                statusClass = 'session-weekend';
                updateCountdown(sessionPrefix, isWeekendClosed.secondsUntilOpen);
            } else {
                // التحقق من حالة الجلسة العادية
                if (closeHour < openHour) {
                    // جلسة تعبر منتصف الليل (مثل سيدني في GMT أو نيويورك في توقيت الأردن)
                    isOpen = currentHour >= openHour || currentHour < closeHour;
                } else {
                    // جلسة عادية
                    isOpen = currentHour >= openHour && currentHour < closeHour;
                }

                if (isOpen) {
                    statusText = 'مفتوحة الآن';
                    statusClass = 'session-open';
                    showCountdown = false;
                } else {
                    statusText = 'مغلقة';
                    statusClass = 'session-closed';

                    // حساب الثواني المتبقية للافتتاح
                    const secondsUntilOpen = calculateSecondsUntilOpen(currentHour, openHour, closeHour);
                    updateCountdown(sessionPrefix, secondsUntilOpen);
                }
            }

            // تطبيق التحديثات
            statusElement.className = statusClass;
            const statusTextElement = statusElement.querySelector('.status-text');
            if (statusTextElement) {
                statusTextElement.textContent = statusText;
            }

            // إظهار/إخفاء العداد التنازلي
            if (countdownElement) {
                countdownElement.style.display = showCountdown ? 'block' : 'none';
            }
        }

        // حساب الثواني المتبقية للافتتاح
        function calculateSecondsUntilOpen(currentHour, openHour, closeHour) {
            let hoursUntilOpen;

            if (closeHour < openHour) {
                // جلسة تعبر منتصف الليل
                if (currentHour < closeHour) {
                    hoursUntilOpen = openHour - currentHour;
                } else {
                    hoursUntilOpen = (24 - currentHour) + openHour;
                }
            } else {
                // جلسة عادية
                if (currentHour < openHour) {
                    hoursUntilOpen = openHour - currentHour;
                } else {
                    hoursUntilOpen = (24 - currentHour) + openHour;
                }
            }

            return Math.max(0, hoursUntilOpen * 3600); // تحويل إلى ثواني
        }

        // تحديث العداد التنازلي
        function updateCountdown(sessionPrefix, secondsUntilOpen) {
            const hoursElement = document.getElementById(sessionPrefix + '-hours');
            const minutesElement = document.getElementById(sessionPrefix + '-minutes');
            const secondsElement = document.getElementById(sessionPrefix + '-seconds');

            if (!hoursElement || !minutesElement || !secondsElement) return;

            // التأكد من أن القيمة موجبة
            const totalSeconds = Math.max(0, Math.floor(secondsUntilOpen));

            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            // تحديث القيم مع تنسيق جميل
            hoursElement.textContent = hours.toString().padStart(2, '0');
            minutesElement.textContent = minutes.toString().padStart(2, '0');
            secondsElement.textContent = seconds.toString().padStart(2, '0');

            // إضافة تأثير نبضة للثواني
            if (seconds !== parseInt(secondsElement.getAttribute('data-last-second') || '0')) {
                secondsElement.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    secondsElement.style.transform = 'scale(1)';
                }, 100);
                secondsElement.setAttribute('data-last-second', seconds.toString());
            }
        }

        // دالة للتحقق من حالة العطلة الأسبوعية
        function checkWeekendStatus(currentTime, isJordanTime = false) {
            let checkTime, marketOpenHour, marketCloseHour;

            if (isJordanTime) {
                checkTime = currentTime;
                marketOpenHour = 1; // الأحد 1:00 صباحاً بتوقيت الأردن
                marketCloseHour = 1; // السبت 1:00 صباحاً بتوقيت الأردن (الجمعة 10 مساءً GMT + 3)
            } else {
                checkTime = currentTime;
                marketOpenHour = 22; // الأحد 10:00 مساءً GMT
                marketCloseHour = 22; // الجمعة 10:00 مساءً GMT
            }

            const currentDay = checkTime.getDay(); // 0 = الأحد, 1 = الاثنين, ..., 6 = السبت
            const currentHour = checkTime.getHours() + (checkTime.getMinutes() / 60) + (checkTime.getSeconds() / 3600);

            if (isJordanTime) {
                // منطق توقيت الأردن
                // السبت: السوق مغلق من 1:00 صباحاً السبت حتى 1:00 صباحاً الأحد
                if (currentDay === 6 && currentHour >= 1) {
                    const hoursUntilSundayOpen = (24 - currentHour) + 1; // حتى الأحد 1:00 صباحاً
                    return {
                        isClosed: true,
                        secondsUntilOpen: hoursUntilSundayOpen * 3600
                    };
                }

                // الأحد: السوق مغلق حتى 1:00 صباحاً
                if (currentDay === 0 && currentHour < 1) {
                    const hoursUntilOpen = 1 - currentHour;
                    return {
                        isClosed: true,
                        secondsUntilOpen: hoursUntilOpen * 3600
                    };
                }

                // الجمعة: السوق يغلق في 1:00 صباحاً السبت (منتصف ليلة الجمعة)
                if ((currentDay === 5 && currentHour >= 1) || (currentDay === 6 && currentHour < 1)) {
                    let hoursUntilSundayOpen;
                    if (currentDay === 5) {
                        hoursUntilSundayOpen = (24 - currentHour) + 24 + 1; // حتى الأحد القادم 1:00 صباحاً
                    } else { // السبت قبل 1:00 صباحاً
                        hoursUntilSundayOpen = (24 - currentHour) + 1; // حتى الأحد 1:00 صباحاً
                    }
                    return {
                        isClosed: true,
                        secondsUntilOpen: hoursUntilSundayOpen * 3600
                    };
                }
            } else {
                // منطق التوقيت العالمي GMT
                // السبت: السوق مغلق تمامًا
                if (currentDay === 6) {
                    const hoursUntilSundayOpen = (24 - currentHour) + 22; // حتى الأحد 10:00 مساءً
                    return {
                        isClosed: true,
                        secondsUntilOpen: hoursUntilSundayOpen * 3600
                    };
                }

                // الأحد: السوق مغلق حتى 10:00 مساءً
                if (currentDay === 0 && currentHour < 22) {
                    const hoursUntilOpen = 22 - currentHour;
                    return {
                        isClosed: true,
                        secondsUntilOpen: hoursUntilOpen * 3600
                    };
                }

                // الجمعة: السوق يغلق في 10:00 مساءً
                if (currentDay === 5 && currentHour >= 22) {
                    const hoursUntilSundayOpen = (24 - currentHour) + 24 + 22; // حتى الأحد القادم 10:00 مساءً
                    return {
                        isClosed: true,
                        secondsUntilOpen: hoursUntilSundayOpen * 3600
                    };
                }
            }

            return {
                isClosed: false,
                secondsUntilOpen: 0
            };
        }

        // دالة لتحديث الحالة العامة للسوق
        function updateGeneralMarketStatus() {
            const now = new Date();
            const utcTime = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
            const weekendStatus = checkWeekendStatus(utcTime);

            const generalStatusElement = document.getElementById('market-general-status');
            const statusTextElement = document.getElementById('general-status-text');

            if (!generalStatusElement || !statusTextElement) return;

            if (weekendStatus.isClosed) {
                // السوق في عطلة أسبوعية
                generalStatusElement.className = 'session-closed';
                generalStatusElement.style.background = 'rgba(239, 68, 68, 0.2)';
                generalStatusElement.style.color = '#ef4444';
                generalStatusElement.style.border = '1px solid rgba(239, 68, 68, 0.3)';
                statusTextElement.textContent = 'السوق مغلق - عطلة أسبوعية';
            } else {
                // السوق في أيام العمل - التحقق من الجلسات المفتوحة
                const currentHour = utcTime.getHours() + (utcTime.getMinutes() / 60);
                let openSessions = 0;

                // فحص الجلسات
                if (currentHour >= 0 && currentHour < 9) openSessions++; // طوكيو
                if (currentHour >= 8 && currentHour < 17) openSessions++; // لندن
                if (currentHour >= 13.5 && currentHour < 22) openSessions++; // نيويورك
                if (currentHour >= 22 || currentHour < 7) openSessions++; // سيدني

                if (openSessions > 0) {
                    generalStatusElement.className = 'session-open';
                    generalStatusElement.style.background = 'rgba(34, 197, 94, 0.2)';
                    generalStatusElement.style.color = '#22c55e';
                    generalStatusElement.style.border = '1px solid rgba(34, 197, 94, 0.3)';

                    if (openSessions === 1) {
                        statusTextElement.textContent = 'جلسة واحدة مفتوحة';
                    } else {
                        statusTextElement.textContent = `${openSessions} جلسات مفتوحة`;
                    }
                } else {
                    generalStatusElement.className = 'session-closed';
                    generalStatusElement.style.background = 'rgba(239, 68, 68, 0.2)';
                    generalStatusElement.style.color = '#ef4444';
                    generalStatusElement.style.border = '1px solid rgba(239, 68, 68, 0.3)';
                    statusTextElement.textContent = 'جميع الجلسات مغلقة';
                }
            }
        }

        // تشغيل العداد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function () {
            // إعداد السويتش
            const timezoneSwitch = document.getElementById('timezone-switch');
            if (timezoneSwitch) {
                timezoneSwitch.addEventListener('change', function () {
                    useJordanTime = this.checked;
                    updateMarketSessions(); // تحديث فوري عند التغيير
                });
            }

            // إعداد القائمة المنسدلة للأدوات
            const toolsDropdown = document.getElementById('toolsDropdown');
            const dropdownMenu = toolsDropdown?.nextElementSibling;

            if (toolsDropdown && dropdownMenu) {
                // إظهار القائمة عند hover
                toolsDropdown.parentElement.addEventListener('mouseenter', function () {
                    dropdownMenu.classList.add('show');
                });

                // إخفاء القائمة عند مغادرة المنطقة
                toolsDropdown.parentElement.addEventListener('mouseleave', function () {
                    dropdownMenu.classList.remove('show');
                });

                // منع إغلاق القائمة عند النقر عليها
                dropdownMenu.addEventListener('click', function (e) {
                    e.stopPropagation();
                });
            }

            updateMarketSessions();
            // تحديث كل ثانية
            setInterval(updateMarketSessions, 1000);
        });

        // تحديث عند تغيير التبويب (للتأكد من الدقة)
        document.addEventListener('visibilitychange', function () {
            if (!document.hidden) {
                updateMarketSessions();
            }
        });
    </script>
</body>

</html>