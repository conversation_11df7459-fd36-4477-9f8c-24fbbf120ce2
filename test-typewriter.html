<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الآلة الكاتبة</title>
    <style>
        body {
            background: #0a192f;
            color: #ccd6f6;
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            padding: 50px;
            line-height: 1.8;
        }
        
        #typed-about {
            min-height: 300px;
            max-width: 1000px;
            width: 100%;
            line-height: 2;
            font-size: 1.3rem;
            color: #ccd6f6;
            text-align: right;
            direction: rtl;
            font-family: 'Cairo', 'Taja<PERSON>', Arial, sans-serif;
            padding: 40px;
            position: relative;
            border: 2px solid #64ffda;
            border-radius: 15px;
            margin: 30px auto;
            background: rgba(17, 34, 64, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .typing-cursor {
            animation: blink 1s infinite;
            color: #64ffda;
            font-weight: bold;
            font-size: 1.2em;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        .typing {
            position: relative;
        }

        .typing::after {
            content: '';
            position: absolute;
            right: -2px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #64ffda;
            animation: blink 1s infinite;
        }
        
        .test-button {
            background: #64ffda;
            color: #0a192f;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>اختبار الآلة الكاتبة</h1>
    
    <button class="test-button" onclick="startTypewriter()">تشغيل الآلة الكاتبة</button>
    <button class="test-button" onclick="clearText()">مسح النص</button>
    
    <div id="typed-about"></div>
    
    <div id="console-output" style="background: #112240; padding: 20px; border-radius: 10px; margin-top: 20px; font-family: monospace; font-size: 12px;">
        <h3>وحدة التحكم:</h3>
        <div id="console-log"></div>
    </div>

    <script>
        // إعادة تعريف console.log لعرض الرسائل في الصفحة
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logDiv = document.getElementById('console-log');
            logDiv.innerHTML += '<div>' + args.join(' ') + '</div>';
            logDiv.scrollTop = logDiv.scrollHeight;
        };

        const aboutText = "نحن في فوركس الأردن نمتلك خبرة تزيد عن 15 عاماً في تداول الأسواق المالية، متخصصون في تحليل الأسواق وتقديم إشارات تداول دقيقة لأزواج العملات، العملات الرقمية، المعادن والمؤشرات العالمية. نقدم لعملائنا تحليلات يومية وإشارات تداول مدروسة بدقة، مع خطط إدارة رأس المال التي تضمن تحقيق أعلى العوائد بأقل المخاطر الممكنة. فريقنا من المحللين المحترفين يعمل على مدار الساعة لتقديم أفضل الفرص الاستثمارية في الأسواق العالمية. نحن نؤمن بأن التداول الناجح يتطلب استراتيجية محكمة وإدارة مخاطر دقيقة، لذلك نقدم لعملائنا خطط تداول شاملة تتناسب مع مستوى خبرتهم ورأس مالهم. انضم إلى آلاف المتداولين الذين حققوا أرباحاً مستدامة من خلال إشاراتنا الدقيقة ونصائحنا الاحترافية.";

        function startTypewriter() {
            console.log("بدء تشغيل الآلة الكاتبة...");
            
            const element = document.getElementById('typed-about');
            if (!element) {
                console.log("العنصر غير موجود!");
                return;
            }
            
            console.log("العنصر موجود، بدء الكتابة...");
            element.innerHTML = '';
            element.style.textAlign = 'right';
            element.style.direction = 'rtl';
            element.style.color = '#ccd6f6';
            element.style.lineHeight = '1.8';
            element.style.fontSize = '1.15rem';
            
            let i = 0;
            function typeChar() {
                if (i < aboutText.length) {
                    element.innerHTML = aboutText.substring(0, i + 1) + '<span class="typing-cursor">|</span>';
                    i++;
                    setTimeout(typeChar, 20);
                } else {
                    element.innerHTML = aboutText;
                    console.log("انتهت الآلة الكاتبة بنجاح!");
                }
            }
            typeChar();
        }
        
        function clearText() {
            document.getElementById('typed-about').innerHTML = '';
            document.getElementById('console-log').innerHTML = '';
            console.log("تم مسح النص");
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log("تم تحميل الصفحة");
            setTimeout(startTypewriter, 1000);
        });
    </script>
</body>
</html>
