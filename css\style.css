/* تحسينات عامة للموقع */
.service-card:hover {
    transform: translateY(-10px) scale(1.02) !important;
    border-color: rgba(100, 255, 218, 0.5) !important;
    box-shadow: 0 25px 50px rgba(100, 255, 218, 0.25) !important;
    background: rgba(10, 25, 47, 0.8) !important;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 15px 35px rgba(100, 255, 218, 0.4) !important;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.05) !important;
    background: rgba(100, 255, 218, 0.1) !important;
    border-color: rgba(100, 255, 218, 0.4) !important;
    box-shadow: 0 10px 25px rgba(100, 255, 218, 0.2) !important;
}

/* تحسين الانيميشن العام */
@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-15px) rotate(2deg);
    }
}

/* التكيف مع الشاشات الصغيرة */
@media (max-width: 992px) {
    .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .three-d-elements {
        min-height: 300px;
        margin-top: 50px;
    }

    .contact-card-3d {
        padding: 30px;
    }

    .three-d-plan-card.featured {
        transform: translateY(0) translateZ(30px);
    }

    .three-d-plan-card {
        margin-bottom: 30px;
    }

    #3d-plans {
        padding: 80px 0;
    }
}

/* تحسينات شاملة للأجهزة اللوحية */
@media (max-width: 1200px) {
    .container {
        max-width: 95% !important;
    }

    .hero-section h1 {
        font-size: 3.2rem !important;
    }

    .hero-section p {
        font-size: 1.2rem !important;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 992px) {
    .splash-content {
        padding: 30px !important;
        max-width: 92% !important;
    }

    .splash-content h2 {
        font-size: 1.8rem !important;
    }

    .feature-card {
        margin-bottom: 15px;
        padding: 15px !important;
    }

    .hero-section h1 {
        font-size: 2.8rem !important;
        line-height: 1.3 !important;
    }

    .hero-section p {
        font-size: 1.1rem !important;
    }

    .glass-card {
        padding: 1.5rem !important;
    }

    .service-card {
        margin-bottom: 20px;
    }

    .navbar-nav {
        text-align: center;
    }

    .nav-link {
        padding: 10px 15px !important;
    }
}

/* تحسينات شاملة للهواتف */
@media (max-width: 768px) {
    /* تحسين العناصر العامة */
    body {
        font-size: 14px;
    }

    .container {
        padding: 0 10px !important;
    }

    /* تحسين شاشة البداية */
    .splash-content {
        padding: 20px !important;
        margin: 5px !important;
        max-width: 98% !important;
        border-radius: 12px !important;
    }

    .splash-content h2 {
        font-size: 1.5rem !important;
        margin-bottom: 15px !important;
    }

    .splash-content p {
        font-size: 0.9rem !important;
        margin-bottom: 10px !important;
    }

    .splash-btn {
        padding: 12px 25px !important;
        font-size: 0.9rem !important;
        margin: 5px !important;
        width: 100% !important;
        max-width: 250px !important;
        border-radius: 8px !important;
    }

    /* تحسين قسم الهيرو */
    .hero-section {
        padding: 60px 0 !important;
    }

    .hero-section h1 {
        font-size: 2.2rem !important;
        line-height: 1.2 !important;
        margin-bottom: 1rem !important;
    }

    .hero-section p {
        font-size: 1rem !important;
        margin-bottom: 1.5rem !important;
    }

    .hero-section a {
        padding: 12px 25px !important;
        font-size: 0.9rem !important;
        margin: 5px !important;
        display: block !important;
        max-width: 250px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    /* تحسين شريط التنقل */
    .navbar {
        padding: 0.5rem 1rem !important;
    }

    .navbar-brand img {
        height: 35px !important;
    }

    .navbar-toggler {
        border: none !important;
        padding: 4px 8px !important;
    }

    .navbar-nav {
        text-align: center;
        padding: 10px 0;
    }

    .nav-link {
        padding: 8px 15px !important;
        font-size: 0.9rem !important;
    }

    /* تحسين بطاقات الخدمات */
    .service-card {
        margin-bottom: 20px;
        padding: 1.5rem !important;
        border-radius: 15px !important;
    }

    .service-icon {
        width: 60px !important;
        height: 60px !important;
        margin-bottom: 1rem !important;
    }

    .service-title {
        font-size: 1.3rem !important;
        margin-bottom: 0.8rem !important;
    }

    .service-text {
        font-size: 0.9rem !important;
    }

    /* تحسين قسم من نحن */
    .glass-card {
        padding: 1.2rem !important;
        margin-bottom: 20px !important;
        border-radius: 15px !important;
    }

    .section-title {
        font-size: 1.8rem !important;
        margin-bottom: 1rem !important;
    }

    #typed-about {
        font-size: 1rem !important;
        line-height: 1.6 !important;
    }

    /* تحسين الإحصائيات */
    .stats {
        flex-direction: column !important;
        gap: 10px !important;
        margin-top: 1.5rem !important;
    }

    .stat-item {
        margin: 0 !important;
        flex: none !important;
        padding: 15px !important;
        border-radius: 12px !important;
    }

    .stat-item h3 {
        font-size: 2.2rem !important;
        margin-bottom: 5px !important;
    }

    .stat-item p {
        font-size: 0.9rem !important;
    }

    /* تحسين الباقات */
    .three-d-plan-card {
        margin-bottom: 25px !important;
        padding: 1.5rem !important;
        transform: none !important;
    }

    .plan-header h3 {
        font-size: 1.4rem !important;
    }

    .plan-header div {
        font-size: 2rem !important;
    }

    .plan-features li {
        font-size: 0.9rem !important;
        padding: 8px 0 !important;
    }

    /* تحسين قسم التواصل */
    .contact-card-3d {
        padding: 1.5rem !important;
        margin-bottom: 20px !important;
        transform: none !important;
    }

    /* تحسين الفوتر */
    .footer-about {
        text-align: center;
        margin-bottom: 20px;
    }

    .social-links {
        justify-content: center;
        margin-top: 15px;
    }

    .social-links a {
        margin: 0 8px !important;
        width: 40px !important;
        height: 40px !important;
        font-size: 1.1rem !important;
    }

    /* تحسين العناوين العامة */
    h1 {
        font-size: 2rem !important;
    }

    h2 {
        font-size: 1.6rem !important;
    }

    h3 {
        font-size: 1.3rem !important;
    }

    h4 {
        font-size: 1.1rem !important;
    }
}

/* تحسينات للهواتف الصغيرة جداً */
@media (max-width: 480px) {
    /* تحسين شاشة البداية للهواتف الصغيرة */
    .splash-content {
        padding: 15px !important;
        margin: 3px !important;
        max-width: 99% !important;
    }

    .splash-content h2 {
        font-size: 1.3rem !important;
        margin-bottom: 12px !important;
    }

    .splash-content p {
        font-size: 0.8rem !important;
        margin-bottom: 8px !important;
    }

    .splash-btn {
        padding: 10px 20px !important;
        font-size: 0.8rem !important;
        max-width: 200px !important;
    }

    /* تحسين قسم الهيرو للهواتف الصغيرة */
    .hero-section h1 {
        font-size: 1.8rem !important;
        line-height: 1.1 !important;
    }

    .hero-section p {
        font-size: 0.9rem !important;
    }

    .hero-section a {
        padding: 10px 20px !important;
        font-size: 0.8rem !important;
        max-width: 200px !important;
    }

    /* تحسين بطاقات الخدمات للهواتف الصغيرة */
    .service-card {
        padding: 1rem !important;
    }

    .service-icon {
        width: 50px !important;
        height: 50px !important;
    }

    .service-title {
        font-size: 1.1rem !important;
    }

    .service-text {
        font-size: 0.8rem !important;
    }

    /* تحسين الإحصائيات للهواتف الصغيرة */
    .stat-item {
        padding: 12px !important;
    }

    .stat-item h3 {
        font-size: 1.8rem !important;
    }

    .stat-item p {
        font-size: 0.8rem !important;
    }

    /* تحسين العناوين للهواتف الصغيرة */
    h1 {
        font-size: 1.6rem !important;
    }

    h2 {
        font-size: 1.4rem !important;
    }

    h3 {
        font-size: 1.2rem !important;
    }

    /* تحسين شريط التنقل للهواتف الصغيرة */
    .navbar-brand img {
        height: 30px !important;
    }

    .nav-link {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
    }
}