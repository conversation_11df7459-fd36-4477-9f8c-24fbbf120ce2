// حل فوري للآلة الكاتبة - يعمل حتى قبل تحميل DOM
function immediateTypewriter() {
    const aboutText = "نحن في فوركس الأردن نمتلك خبرة تزيد عن 15 عاماً في تداول الأسواق المالية، متخصصون في تحليل الأسواق وتقديم إشارات تداول دقيقة لأزواج العملات، العملات الرقمية، المعادن والمؤشرات العالمية. نقدم لعملائنا تحليلات يومية وإشارات تداول مدروسة بدقة، مع خطط إدارة رأس المال التي تضمن تحقيق أعلى العوائد بأقل المخاطر الممكنة. فريقنا من المحللين المحترفين يعمل على مدار الساعة لتقديم أفضل الفرص الاستثمارية في الأسواق العالمية. نحن نؤمن بأن التداول الناجح يتطلب استراتيجية محكمة وإدارة مخاطر دقيقة، لذلك نقدم لعملائنا خطط تداول شاملة تتناسب مع مستوى خبرتهم ورأس مالهم. انضم إلى آلاف المتداولين الذين حققوا أرباحاً مستدامة من خلال إشاراتنا الدقيقة ونصائحنا الاحترافية.";

    function tryTypewriter() {
        const element = document.getElementById('typed-about');
        if (!element) {
            setTimeout(tryTypewriter, 100);
            return;
        }

        console.log("Immediate typewriter found element, starting...");
        element.style.textAlign = 'right';
        element.style.direction = 'rtl';
        element.style.color = '#ccd6f6';
        element.style.lineHeight = '1.8';
        element.style.fontSize = '1.15rem';

        let i = 0;
        function typeChar() {
            if (i < aboutText.length) {
                element.innerHTML = aboutText.substring(0, i + 1) + '<span style="color: #64ffda; animation: blink 1s infinite;">|</span>';
                i++;
                setTimeout(typeChar, 20);
            } else {
                element.innerHTML = aboutText;
            }
        }
        typeChar();
    }

    tryTypewriter();
}

// تشغيل فوري
setTimeout(immediateTypewriter, 500);

document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM Content Loaded - Starting main.js");
    // تحسين إغلاق الـ splash screen
    const splashScreen = document.getElementById('splashScreen');
    const closeSplash = document.getElementById('closeSplash');

    if (closeSplash) {
        closeSplash.addEventListener('click', function() {
            splashScreen.style.opacity = '0';
            splashScreen.style.transform = 'scale(0.95)';
            setTimeout(() => {
                splashScreen.style.display = 'none';
            }, 600);
        });
    }

    // إغلاق تلقائي بعد 12 ثانية مع تأثير محسن
    setTimeout(() => {
        if (splashScreen && splashScreen.style.display !== 'none') {
            splashScreen.style.opacity = '0';
            splashScreen.style.transform = 'scale(0.95)';
            setTimeout(() => {
                splashScreen.style.display = 'none';
            }, 600);
        }
    }, 12000);

    // إضافة تأثيرات دخول للعناصر عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // تطبيق المراقب على العناصر (باستثناء الصورة)
    document.querySelectorAll('.service-card, .stat-item, .glass-card').forEach(el => {
        // تجاهل الصورة في قسم "من نحن"
        if (!el.closest('.about-image')) {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.8s ease-out';
            observer.observe(el);
        }
    });

    // انتظار تحميل Bootstrap قبل إعداد التنقل
    setTimeout(setupNavigation, 100);

    // إضافة تأثيرات تفاعلية للأزرار
    document.querySelectorAll('.splash-btn, .hero-section a, .3d-plan-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // تحسين تأثيرات البطاقات
    document.querySelectorAll('.service-card, .feature-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // النص الكامل الأصلي لقسم من نحن
    const aboutText = 
    "في فوركس الأردن، نمتلك أكثر من 15 سنة خبرة في الأسواق المالية. " +
    "نقدّم إشارات تداول دقيقة وتحليلات يومية لأزواج العملات، الكريبتو، المعادن، والمؤشرات. " +
    "فريقنا يعمل 24/7 لرصد أفضل الفرص مع خطط إدارة رأس مال ذكية لتقليل المخاطر وزيادة الأرباح. " +
    "انضم لآلاف المتداولين اللي حققوا أرباح مستدامة معنا. " +
    "ابدأ تداولك بثقة مع فوركس الأردن.";

console.log("About text loaded:", aboutText.substring(0, 50) + "...");


    // آلة كاتبة محسنة مع ضمان ظهور النص
    function typeWriterEffect() {
        const element = document.getElementById('typed-about');
        if (!element) {
            console.error("Element with ID 'typed-about' not found!");
            // محاولة العثور على العنصر مرة أخرى بعد ثانية
            setTimeout(typeWriterEffect, 1000);
            return;
        }

        console.log("Starting typewriter effect for element:", element);

        // إعداد النص للغة العربية (RTL)
        element.style.textAlign = 'right';
        element.style.direction = 'rtl';
        element.style.minHeight = '150px';

        // نسخ النص الاحتياطي في حال فشل الانيميشن
        element.setAttribute('data-backup-text', aboutText);

        const text = aboutText;
        let i = 0;
        let isTyping = false;

        // التأكد من أن العنصر فارغ
        element.innerHTML = '';

        function typeChar() {
            if (isTyping) return; // منع التشغيل المتعدد
            isTyping = true;

            if (i < text.length) {
                const currentText = text.substring(0, i + 1);
                element.innerHTML = currentText + '<span class="typing-cursor">|</span>';
                i++;

                // سرعة متغيرة لمظهر أكثر طبيعية
                const speed = Math.random() * 30 + 20; // سرعة أسرع
                setTimeout(() => {
                    isTyping = false;
                    typeChar();
                }, speed);
            } else {
                // التأكد من عرض النص كاملاً في النهاية
                element.innerHTML = text;
                element.classList.remove('typing');
                console.log("Typewriter effect completed");
            }
        }

        // بدء الانيميشن
        element.classList.add('typing');
        console.log("Starting to type text:", text.substring(0, 50) + "...");
        typeChar();

        // نسخ احتياطي بعد 15 ثانية في حال لم يكتمل
        setTimeout(() => {
            if (element.innerHTML.length < text.length * 0.8) { // إذا لم يكتمل 80% من النص
                console.log("Typewriter timeout - showing full text");
                element.innerHTML = text;
                element.classList.remove('typing');
            }
        }, 15000);
    }

    // طرق تشغيل الآلة الكاتبة
    let typewriterStarted = false; // منع التشغيل المتعدد

    function initTypeWriter() {
        if (typewriterStarted) {
            console.log("Typewriter already started, skipping...");
            return;
        }

        // الانتظار حتى يكون القسم ظاهراً
        const aboutSection = document.getElementById('about');
        const typedElement = document.getElementById('typed-about');

        if (!aboutSection) {
            console.warn("About section not found, retrying in 1 second...");
            setTimeout(initTypeWriter, 1000);
            return;
        }

        if (!typedElement) {
            console.warn("Typed element not found, retrying in 1 second...");
            setTimeout(initTypeWriter, 1000);
            return;
        }

        console.log("About section and typed element found, setting up observer");

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !typewriterStarted) {
                    console.log("About section is visible, starting typewriter");
                    typewriterStarted = true;
                    setTimeout(typeWriterEffect, 500); // تأخير قصير للتأكد
                    observer.unobserve(aboutSection);
                }
            });
        }, {threshold: 0.2}); // زيادة threshold قليلاً

        observer.observe(aboutSection);
    }

    // تشغيل عند تحميل الصفحة
    setTimeout(initTypeWriter, 1000); // تقليل التأخير

    // تشغيل عند النقر على رابط "من نحن"
    document.addEventListener('click', function(e) {
        if (e.target.closest('a[href="#about"]')) {
            console.log("About link clicked");
            setTimeout(() => {
                if (!typewriterStarted) {
                    initTypeWriter();
                }
            }, 500);
        }
    });

    // تشغيل فوري إذا كان القسم مرئياً بالفعل
    setTimeout(() => {
        const aboutSection = document.getElementById('about');
        if (aboutSection && !typewriterStarted) {
            const rect = aboutSection.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                console.log("About section is already visible, starting typewriter immediately");
                typewriterStarted = true;
                typeWriterEffect();
            }
        }
    }, 2000);

    // نسخة احتياطية نهائية - عرض النص مباشرة بعد 3 ثواني
    setTimeout(() => {
        const typedElement = document.getElementById('typed-about');
        if (typedElement && (!typedElement.innerHTML || typedElement.innerHTML.trim() === '')) {
            console.log("Fallback: Showing text directly without typewriter effect");
            typedElement.innerHTML = aboutText;
            typedElement.style.textAlign = 'right';
            typedElement.style.direction = 'rtl';
            typedElement.style.color = '#ccd6f6';
            typedElement.style.lineHeight = '1.8';
        }
    }, 3000);

    // حل بديل فوري - تشغيل مباشر عند تحميل الصفحة
    function simpleTypewriter() {
        const element = document.getElementById('typed-about');
        if (!element) {
            console.log("Element not found, retrying...");
            setTimeout(simpleTypewriter, 500);
            return;
        }

        console.log("Simple typewriter starting...");
        element.style.textAlign = 'right';
        element.style.direction = 'rtl';
        element.style.color = '#ccd6f6';
        element.style.lineHeight = '1.8';

        let index = 0;
        const speed = 25; // سرعة أسرع

        function type() {
            if (index < aboutText.length) {
                element.innerHTML = aboutText.substring(0, index + 1) + '<span style="color: #64ffda; animation: blink 1s infinite;">|</span>';
                index++;
                setTimeout(type, speed);
            } else {
                element.innerHTML = aboutText;
                console.log("Simple typewriter completed");
            }
        }

        type();
    }

    // تشغيل الحل البديل البسيط
    setTimeout(simpleTypewriter, 1500);

    // تأثيرات الحركة
    const style = document.createElement('style');
    style.textContent = `
        @keyframes moveWaves {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* حركات الموجات */
        @keyframes wave-animation {
            0% { transform: translateX(0) scaleY(1); }
            50% { transform: translateX(-25%) scaleY(0.8); }
            100% { transform: translateX(-50%) scaleY(1); }
        }

        @keyframes card-wave {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين تأثيرات الهافر */
        .3d-plan-card {
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .3d-plan-card:hover {
            transform: translateY(-15px) translateZ(50px) rotateY(8deg) !important;
            box-shadow: 0 35px 70px rgba(0, 0, 0, 0.5) !important;
            border-color: rgba(100, 255, 218, 0.6) !important;
        }

        .3d-plan-card.featured:hover {
            box-shadow: 0 40px 80px rgba(100, 255, 218, 0.4) !important;
            transform: translateY(-20px) translateZ(60px) rotateY(10deg) !important;
        }

        .3d-plan-btn:hover .btn-wave {
            animation: wave-run 1.2s ease-in-out infinite;
        }

        @keyframes wave-run {
            0% { transform: translateX(-100%); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }

        /* تحسين تأثيرات الأزرار العامة */
        .splash-btn:hover,
        .hero-section a:hover {
            transform: translateY(-5px) scale(1.08) !important;
            box-shadow: 0 20px 50px rgba(100, 255, 218, 0.7) !important;
            filter: brightness(1.1);
        }

        /* تأثيرات الهافر الفاخرة */
        .luxury-partner-card:hover {
            transform: translateY(-10px) scale(1.02) !important;
            border-color: rgba(100, 255, 218, 0.4) !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.35) !important;
        }

        .luxury-partner-card:hover .luxury-card-overlay {
            opacity: 1 !important;
        }

        .luxury-partner-card:hover .luxury-partner-img-container img {
            transform: scale(1.15);
            filter: brightness(1.2) !important;
        }

        .luxury-partner-card:hover .luxury-img-frame {
            opacity: 1 !important;
            transform: scale(1.05);
        }

        .luxury-partner-card:hover p {
            color: #64ffda !important;
            text-shadow: 0 0 10px rgba(100, 255, 218, 0.3);
        }

        /* تأثيرات دخول عند التمرير */
        @keyframes luxuryFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px) rotateX(20deg);
                filter: blur(5px);
            }
            to {
                opacity: 1;
                transform: translateY(0) rotateX(0);
                filter: blur(0);
            }
        }

        .luxury-partner-card {
            animation: luxuryFadeIn 0.8s ease-out forwards;
            opacity: 0;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .luxury-partner-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .luxury-partner-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .luxury-partner-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .luxury-partner-card:nth-child(4) {
            animation-delay: 0.4s;
        }

        .luxury-partner-card:nth-child(5) {
            animation-delay: 0.5s;
        }

        .luxury-partner-card:nth-child(6) {
            animation-delay: 0.6s;
        }

        .luxury-partner-card:nth-child(7) {
            animation-delay: 0.7s;
        }

        .luxury-partner-card:nth-child(8) {
            animation-delay: 0.8s;
        }

        /* تأثيرات النص */
        .section-title span {
            display: inline-block;
            transition: all 0.4s ease;
        }

        .section-title:hover span {
            transform: translateY(-3px);
        }

        /* حركة العناصر ثلاثية الأبعاد */
        @keyframes float {
            0%, 100% {
                transform: translate(-50%, -50%) rotateX(60deg) rotateZ(45deg) translateY(0);
            }
            50% {
                transform: translate(-50%, -50%) rotateX(60deg) rotateZ(45deg) translateY(-20px);
            }
        }

        /* تأثيرات الهافر على البطاقات */
        .contact-method-card:hover {
            transform: translateZ(30px) !important;
            border-color: rgba(100, 255, 218, 0.4) !important;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
            background: rgba(17, 34, 64, 0.7) !important;
        }

        .contact-card-3d:hover {
            transform: perspective(1000px) rotateY(5deg) !important;
        }

        /* Hover Effects */
        .social-links a:hover {
            background: rgba(100, 255, 218, 0.2) !important;
            transform: translateY(-3px);
        }

        ul li a:hover {
            color: #64ffda !important;
            transform: translateX(5px);
        }

        /* أنماط الآلة الكاتبة */
        #typed-about {
            min-height: 150px;
            max-width: 100%;
            width: 100%;
            line-height: 1.8;
            font-size: 1.1rem;
            color: #ccd6f6;
            text-align: right;
            direction: rtl;
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            padding: 15px 0;
            position: relative;
            margin: 15px 0;
            display: block;
        }

        .typing-cursor {
            animation: blink 1s infinite;
            color: #64ffda;
            font-weight: bold;
            font-size: 1.2em;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        .typing {
            position: relative;
        }

        .typing::after {
            content: '';
            position: absolute;
            right: -2px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #64ffda;
            animation: blink 1s infinite;
        }

        /* ثبات الصورة */
        .about-image,
        .about-image img,
        .about-image * {
            transform: none !important;
            transition: none !important;
            animation: none !important;
        }
    `;
    document.head.appendChild(style);
});

// دالة إعداد التنقل
function setupNavigation() {
    // تحديث الـ active link في الـ navigation
    function updateActiveLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');

        let current = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.pageYOffset >= (sectionTop - 150)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    }

    // تشغيل التحديث عند التمرير
    window.addEventListener('scroll', updateActiveLink);

    // تشغيل التحديث عند تحميل الصفحة
    updateActiveLink();

    // إضافة تأثير smooth scroll للروابط
    const navLinks = document.querySelectorAll('a[href^="#"]');

    navLinks.forEach((anchor) => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');

            // تجاهل الروابط الفارغة أو التي تحتوي على # فقط
            if (!href || href === '#' || href === '#!') {
                return;
            }

            e.preventDefault();
            const target = document.querySelector(href);

            if (target) {
                // إغلاق الـ mobile menu إذا كان مفتوح
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                    // استخدام Bootstrap API
                    const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse) || new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }

                const offsetTop = target.offsetTop - 80; // تعديل للـ navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // تحسين تأثيرات الـ navbar عند التمرير ومؤشر التقدم
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    const scrollProgress = document.getElementById('scrollProgress');

    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // تغيير شفافية الـ navbar حسب التمرير
        if (scrollTop > 100) {
            navbar.style.background = 'rgba(10, 25, 47, 0.95)';
            navbar.style.backdropFilter = 'blur(15px)';
        } else {
            navbar.style.background = 'rgba(10, 25, 47, 0.85)';
            navbar.style.backdropFilter = 'blur(10px)';
        }

        // تحديث مؤشر تقدم التمرير
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / scrollHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';

        lastScrollTop = scrollTop;
    });

    // إضافة تأثيرات تحميل سلسة
    window.addEventListener('load', function() {
        document.body.style.opacity = '1';
        document.body.style.transition = 'opacity 0.5s ease-in-out';
    });

    // تحسين الأداء للانيميشن
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    if (prefersReducedMotion.matches) {
        // تقليل الانيميشن للمستخدمين الذين يفضلون ذلك
        document.querySelectorAll('*').forEach(el => {
            el.style.animationDuration = '0.01ms !important';
            el.style.animationIterationCount = '1 !important';
            el.style.transitionDuration = '0.01ms !important';
        });
    }
}